package com.ehome.oc.controller.wx;

import com.alibaba.fastjson.JSONObject;
import com.ehome.common.core.controller.BaseWxController;
import com.ehome.common.core.domain.AjaxResult;
import com.ehome.common.core.domain.model.LoginUser;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.common.utils.cache.ConcurrentHashMapCache;
import com.ehome.common.utils.sql.EasySQL;
import com.ehome.oc.domain.HouseInfo;
import com.ehome.oc.domain.OwnerInfo;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.service.IHouseInfoService;
import com.ehome.oc.service.IWxUserService;
import com.ehome.system.domain.SysNotice;
import com.ehome.system.service.ISysNoticeService;
import com.ehome.framework.manager.AsyncManager;
import com.ehome.framework.manager.factory.AsyncFactory;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.activerecord.Record;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RequestMapping("/api/wx/index")
@RestController
public class WxIndexController extends BaseWxController {

    @Autowired
    private IWxUserService wxUserService;

    @Autowired
    private ISysNoticeService noticeService;

    @Autowired
    private IHouseInfoService houseInfoService;

    @Autowired
    private ConcurrentHashMapCache cache;

    /**
     * 获取认证状态
     */
    @RequestMapping("/status")
    public AjaxResult status() {
        JSONObject params =  getParams();
        Map<String, Object> data = new HashMap<>();
        try {
           logger.info("获取认证状态参数: " + params);
           LoginUser currentUser = getCurrentUser();

           // 检查用户类型
           if ("2".equals(currentUser.getUserType())) {
               // 物业用户，返回简化的状态信息
               data.put("isPropertyUser", true);
               data.put("userType", "2");
               data.put("tokenUser", currentUser);
               data.put("isHouseAuth", true); // 物业用户默认认证通过
               return AjaxResult.success(data);
           }

           String ownerId = currentUser.getOwnerId();

           OwnerInfo ownerInfo = new OwnerInfo();
           List<HouseInfo> houseList = new ArrayList<HouseInfo>();

           List<Record> houseRecordList = Db.find("SELECT t1.*,t2.house_info,t2.owner_id,t2.owner_name,t2.role,t2.mobile,t3.is_default user_default  from eh_house_info t1,eh_owner t2 ,eh_house_owner_rel t3 where t1.house_id = t3.house_id and  t2.owner_id = t3.owner_id and t2.owner_id = ?",ownerId);
           if(houseRecordList!=null){
                for(Record record:houseRecordList){
                    HouseInfo house = houseInfoService.recordToObj(record);
                    houseList.add(house);
                    ownerInfo.setOwnerId(record.getStr("owner_id"));
                    ownerInfo.setOwnerName(record.getStr("owner_name"));
                    ownerInfo.setCommunityId(record.getStr("community_id"));
                    ownerInfo.setMobile(record.getStr("mobile"));
                    ownerInfo.setRole(record.getStr("role"));
                    ownerInfo.setHouseStr(record.getStr("house_info"));
                }
           }

            // 查找默认房屋
            HouseInfo defaultHouse = null;
            boolean isHouseAuth = false;

            if (houseList != null && !houseList.isEmpty()) {
                isHouseAuth = true;
                for (HouseInfo house : houseList) {
                    if (house.getIsDefault() != null && house.getIsDefault() == 1) {
                        defaultHouse = house;
                        break;
                    }
                }
                if(defaultHouse==null){
                    defaultHouse = houseList.get(0);
                }
                ownerInfo.setHouseStr(defaultHouse.getCombinaName()+"/"+defaultHouse.getRoom());
            }

            JSONObject communityInfo =  cache.get("communityInfo"+ownerInfo.getCommunityId(), JSONObject.class);
            if(communityInfo==null){
               communityInfo = new JSONObject();
               Record communityRecord = Db.findFirst("select * from eh_community where oc_id = ?",ownerInfo.getCommunityId());
               String communitName = communityRecord.getStr("oc_name");
               ownerInfo.setCommunityName(communitName);
               communityInfo.put("address", communityRecord.get("oc_address"));
               communityInfo.put("servicePhone", communityRecord.get("service_phone"));
               communityInfo.put("extJson", communityRecord.get("ext_json"));
               communityInfo.put("communityId", ownerInfo.getCommunityId());
               communityInfo.put("communityName", ownerInfo.getCommunityName());
               communityInfo.put("communityBanner","/static/images/banner.png");
               // 添加配置更新时间，用于小程序端检查配置是否有更新
               communityInfo.put("updateTime", communityRecord.get("update_time"));
               cache.put("communityInfo"+ownerInfo.getCommunityId(), communityInfo);
            }


            // 检查用户订阅状态
            Map<String, Object> subscribeStatus = checkUserSubscribeStatus();

            data.put("ownerInfo", ownerInfo);
            data.put("communityInfo", communityInfo);
            data.put("isHouseAuth", isHouseAuth);
            data.put("houseInfo", defaultHouse);
            data.put("tokenUser",getCurrentUser());
            data.put("subscribeStatus", subscribeStatus);
            return AjaxResult.success(data);
        } catch (Exception e) {
            logger.error("获取认证状态失败: " + e.getMessage(), e);
            return AjaxResult.error("获取认证状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取菜单列表（首页用，只返回叶子节点）
     */
    @RequestMapping("/getMenus")
    public AjaxResult getMenus() {
       JSONObject params = getParams();
       String source = params.getString("source");

       // 获取所有菜单数据
       List<Map<String,Object>> allMenus = getAllMenuData(source);

       // 递归构建菜单树
       List<Map<String,Object>> menuTree = buildMenuTree(allMenus, 0);

       // 提取叶子节点（没有子菜单的菜单项）
       List<Map<String,Object>> leafMenus = extractLeafMenus(menuTree);

       // 按top_show和sort排序
       leafMenus.sort((a, b) -> {
           int topShowA = Integer.parseInt(String.valueOf(a.get("top_show")));
           int topShowB = Integer.parseInt(String.valueOf(b.get("top_show")));
           if (topShowA != topShowB) {
               return Integer.compare(topShowB, topShowA); // top_show降序
           }
           int sortA = (Integer) a.get("sort");
           int sortB = (Integer) b.get("sort");
           return Integer.compare(sortA, sortB); // sort升序
       });

       return success(leafMenus);
    }

    /**
     * 获取完整菜单列表（包含一二级菜单结构）
     */
    @RequestMapping("/getAllMenus")
    public AjaxResult getAllMenus() {
       JSONObject params = getParams();
       String source = params.getString("source");

       // 获取所有菜单数据
       List<Map<String,Object>> allMenus = getAllMenuData(source);

       // 递归构建菜单树（从根节点开始）
       List<Map<String,Object>> menuTree = buildMenuTree(allMenus, 0);

       return success(menuTree);
    }

    /**
     * 获取所有菜单数据
     */
    private List<Map<String,Object>> getAllMenuData(String source) {
        // 根据用户类型确定菜单来源
        String menuSource = getMenuSourceByUserType(source);

        String sql = "select nav_id,nav_name,icon_name,is_default,tap_name,sort,nav_type,top_show,remark,parent_id from eh_wx_nav where status = 0";
        List<Object> sqlParams = new ArrayList<>();

        // 物业菜单不需要社区限制，使用 'default' 或当前社区
        if ("propertyNav".equals(menuSource)) {
            sql += " and  community_id = ?";
            sqlParams.add(getCurrentUser().getCommunityId());
        } else {
            sql += " and community_id = ?";
            sqlParams.add(getCurrentUser().getCommunityId());
        }

        if (StringUtils.isNotEmpty(menuSource)) {
            sql += " and source = ?";
            sqlParams.add(menuSource);
        }

        sql += " order by parent_id, sort";

        List<Record> list = Db.find(sql, sqlParams.toArray());
        List<Map<String,Object>> listMap = new LinkedList<>();

        for(Record record : list){
            Map<String,Object> menuItem = record.toMap();
            // 确保字段类型正确
            menuItem.put("nav_id", record.get("nav_id"));
            menuItem.put("nav_name", record.getStr("nav_name"));
            menuItem.put("icon_name", record.getStr("icon_name"));
            menuItem.put("is_default", record.getInt("is_default"));
            menuItem.put("tap_name", record.getStr("tap_name"));
            menuItem.put("nav_type", record.getStr("nav_type"));
            menuItem.put("top_show", record.getStr("top_show"));
            menuItem.put("remark", record.getStr("remark"));
            menuItem.put("sort", record.getInt("sort"));
            menuItem.put("parent_id", record.getInt("parent_id"));
            listMap.add(menuItem);
        }
        return listMap;
    }

    /**
     * 根据用户类型确定菜单来源
     */
    private String getMenuSourceByUserType(String originalSource) {
        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser != null && "2".equals(currentUser.getUserType())) {
                // 物业用户，返回物业菜单
                return "propertyNav";
            }
            // 业主用户或其他，返回原始source
            return originalSource;
        } catch (Exception e) {
            // 异常情况下返回原始source
            return originalSource;
        }
    }

    /**
     * 递归构建菜单树
     */
    private List<Map<String,Object>> buildMenuTree(List<Map<String,Object>> allMenus, int parentId) {
        List<Map<String,Object>> result = new ArrayList<>();

        for (Map<String,Object> menu : allMenus) {
            Integer menuParentId = (Integer) menu.get("parent_id");
            if (menuParentId != null && menuParentId == parentId) {
                // 递归获取子菜单
                List<Map<String,Object>> children = buildMenuTree(allMenus, (Integer) menu.get("nav_id"));
                menu.put("children", children);
                menu.put("hasChildren", !children.isEmpty());
                result.add(menu);
            }
        }

        return result;
    }

    /**
     * 提取叶子节点（没有子菜单的菜单项）
     */
    private List<Map<String,Object>> extractLeafMenus(List<Map<String,Object>> menuTree) {
        List<Map<String,Object>> leafMenus = new ArrayList<>();

        for (Map<String,Object> menu : menuTree) {
            @SuppressWarnings("unchecked")
            List<Map<String,Object>> children = (List<Map<String,Object>>) menu.get("children");

            if (children == null || children.isEmpty()) {
                // 叶子节点，添加到结果中
                leafMenus.add(menu);
            } else {
                // 有子菜单，递归提取子菜单中的叶子节点
                leafMenus.addAll(extractLeafMenus(children));
            }
        }

        return leafMenus;
    }

    /**
     * 获取用户信息
     */
    @GetMapping("/info")
    public AjaxResult getUserInfo() {
        try {
            LoginUser loginUser = getCurrentUser();
            // 获取最新的用户信息
            WxUser wxUser = wxUserService.selectWxUserById(loginUser.getUserId());
            if (wxUser == null) {
                return AjaxResult.error("用户不存在");
            }
            Map<String, Object> data = new HashMap<>();
            data.put("userId", wxUser.getUserId());
            data.put("openid", wxUser.getOpenId()); // 添加openid字段
            data.put("nickName", wxUser.getNickName());
            data.put("avatarUrl", wxUser.getAvatarUrl());
            data.put("mobile", wxUser.getMobile());
            data.put("ownerInfo",new HashMap<>());
            if(wxUser.getMobile()!=null){
                Record ownerInfo = Db.findFirst("select * from eh_owner where mobile = ?", wxUser.getMobile());
                if(ownerInfo!=null){
                    data.put("ownerInfo",ownerInfo.toMap());
                }
            }
            return AjaxResult.success(data);
        } catch (Exception e) {
            return AjaxResult.error("获取用户信息失败: " + e.getMessage());
        }
    }


    /**
     * 获取客户端IP地址
     */
    @GetMapping("/getClientIP")
    public AjaxResult getClientIP() {
        try {
            String clientIP = IpUtils.getIpAddr(getRequest());
            Map<String, Object> result = new HashMap<>();
            result.put("ip", clientIP);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取客户端IP失败: " + e.getMessage(), e);
            return AjaxResult.error("获取客户端IP失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告列表
     */
    @PostMapping("/notices")
    public AjaxResult getNotices() {
        try {
            JSONObject params = getParams();
            String noticeType = params.getString("noticeType");
            Integer page = params.getInteger("page");
            Integer pageSize = params.getInteger("pageSize");



            // 设置默认值
            if (page == null || page < 1) {
                page = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 5; // 首页默认5条
            }

            // 构建查询条件
            EasySQL sql = new EasySQL();
            sql.append("from sys_notice where 1=1");
            sql.append(getCurrentUser().getCommunityId(),"and community_id = ?");
            // 注意：noticeType为空字符串表示查询全部，不为空则查询指定类型
            if (StringUtils.isNotEmpty(noticeType)) {
                sql.append(noticeType,"and notice_type = ?");
            }
            sql.append("and status = 0");
            sql.append("and deleted = 0");
            sql.append("order by is_top desc, create_time desc");



            // 使用分页查询
            Page<Record> pageResult = Db.paginate(page, pageSize,
                "select notice_id, notice_title,pms_id,community_id,notice_type, cast(notice_content as char) as notice_content, status, create_by, create_time, update_by, update_time, remark, read_count, comment_count, is_top, deleted, enable_comment, house_read_count",
                sql.toFullSql());
            List<Record> list = pageResult.getList();

            // 从分页结果中获取总数
            int total = pageResult.getTotalRow();

            // 转换为前端需要的格式
            List<Map<String, Object>> noticeList = new ArrayList<>();
            for (Record item : list) {
                Map<String, Object> noticeMap = new HashMap<>();
                noticeMap.put("id", item.getStr("notice_id"));
                noticeMap.put("title", item.getStr("notice_title"));
                noticeMap.put("content", item.getStr("notice_content")); // 列表页可能需要内容
                noticeMap.put("type", item.getStr("notice_type"));
                noticeMap.put("noticeType", item.getStr("notice_type")); // 兼容前端字段
                noticeMap.put("createTime", item.getStr("create_time"));
                noticeMap.put("readCount", item.getInt("read_count"));
                noticeMap.put("houseReadCount", item.getInt("house_read_count"));
                noticeMap.put("isTop", item.getInt("is_top"));
                noticeMap.put("enableComment", item.getInt("enable_comment"));
                noticeList.add(noticeMap);
            }

            Map<String, Object> result = new HashMap<>();
            result.put("list", noticeList);
            result.put("total", total);
            result.put("page", page);
            result.put("pageSize", pageSize);
            result.put("hasMore", total > page * pageSize);

            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取公告列表失败: " + e.getMessage(),e);
            return AjaxResult.error("获取公告列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取公告详情
     */
    @GetMapping("/notice/detail/{id}")
    public AjaxResult getNoticeDetail(@PathVariable("id") Long id) {
        try {
            // 获取当前用户信息和IP地址
            LoginUser currentUser = getCurrentUser();
            String ownerId = currentUser.getOwnerId();
            if(StringUtils.isEmpty(ownerId)){
                Long userId = currentUser != null ? currentUser.getUserId() : null;
                if(userId!=null){
                    ownerId = String.valueOf(userId);
                }
            }
            String userName = currentUser != null ? currentUser.getUsername() : "匿名用户";
            String ipAddress = IpUtils.getIpAddr(getRequest());
            String houseId = currentUser != null ? currentUser.getHouseId() : null;
            String houseName = currentUser != null ? currentUser.getHouseName() : null;

            // 查看公告并记录阅读日志（使用新的重载方法）
            SysNotice notice = noticeService.viewNoticeAndRecord(id, ownerId, userName, ipAddress, ownerId, houseId, houseName);
            if (notice == null) {
                return AjaxResult.error("公告不存在");
            }

            // 查询公告附件
            List<Record> attachmentRecords = Db.find(
                "SELECT ar.*, fi.original_name, fi.access_url, fi.file_size, fi.file_type, fi.mime_type " +
                "FROM eh_attachment_relation ar " +
                "LEFT JOIN eh_file_info fi ON ar.file_id = fi.file_id " +
                "WHERE ar.business_type = 'notice' AND ar.business_id = ? AND ar.status = '0' AND fi.status = '0' " +
                "ORDER BY ar.sort_order ASC, ar.create_time ASC",
                id.toString()
            );

            // 转换附件数据为小程序需要的格式
            List<Map<String, Object>> attachments = new ArrayList<>();
            if (attachmentRecords != null) {
                for (Record record : attachmentRecords) {
                    Map<String, Object> attachment = new HashMap<>();
                    attachment.put("id", record.getStr("file_id"));
                    attachment.put("name", record.getStr("original_name"));
                    attachment.put("url", record.getStr("access_url"));
                    attachment.put("size", record.getLong("file_size"));
                    attachment.put("type", record.getStr("file_type"));
                    attachment.put("mimeType", record.getStr("mime_type"));
                    attachments.add(attachment);
                }
            }

            // 查询发布人姓名
            String authorName = null;
            if (StringUtils.isNotEmpty(notice.getCreateBy())&&StringUtils.isBlank(notice.getSignature())) {
                try {
                    Record userRecord = Db.findFirst("SELECT user_name FROM sys_user WHERE login_name = ?", notice.getCreateBy());
                    if (userRecord != null) {
                        authorName = userRecord.getStr("user_name");
                    }
                } catch (Exception e) {
                    logger.warn("查询发布人姓名失败: " + e.getMessage());
                }
            }

            Map<String, Object> result = new HashMap<>();
            result.put("id", notice.getNoticeId());
            result.put("title", notice.getNoticeTitle());
            result.put("content", notice.getNoticeContent());
            result.put("type", notice.getNoticeType());
            result.put("date", notice.getUpdateTime());
            result.put("createTime", notice.getCreateTime());
            result.put("readCount", notice.getReadCount());
            result.put("isTop", notice.getIsTop());
            result.put("signature", notice.getSignature());
            result.put("publisherType", notice.getPublisherType());
            result.put("authorName", authorName);
            result.put("attachments", attachments);
            result.put("likeCount", notice.getLikeCount());
            result.put("commentCount", notice.getCommentCount());
            result.put("shareCount", notice.getShareCount());
            result.put("enableComment", notice.getEnableComment());
            result.put("houseReadCount", notice.getHouseReadCount());
            return AjaxResult.success(result);
        }catch (Exception e) {
            logger.error("获取公告详情失败: " + e.getMessage(),e);
            return AjaxResult.error("获取公告详情失败: " + e.getMessage());
        }
    }

    /**
     * 检查用户订阅状态
     */
    private Map<String, Object> checkUserSubscribeStatus() {
        Map<String, Object> subscribeStatus = new HashMap<>();

        try {
            LoginUser currentUser = getCurrentUser();
            if (currentUser == null || currentUser.getUserId() == null) {
                subscribeStatus.put("needSubscribe", false);
                subscribeStatus.put("reason", "用户未登录");
                return subscribeStatus;
            }

            // 模板ID常量
            String TEMPLATE_PROPERTY_NOTICE = "oFsuyzgpcAvENaOqokhmD-Fa8EmBpDsh_QFLccPmRUY";

            // 查询用户是否已订阅物业通知
            Record subscribeRecord = Db.findFirst(
                "SELECT * FROM eh_wx_subscribe_record WHERE wx_user_id = ? AND template_id = ? AND status = 1",
                String.valueOf(currentUser.getUserId()), TEMPLATE_PROPERTY_NOTICE
            );

            boolean needSubscribe = true;
            String reason = "未订阅物业通知";

            if (subscribeRecord != null) {
                // 检查是否过期（一次性订阅）
                Date expireTime = subscribeRecord.getDate("expire_time");
                if (expireTime == null || expireTime.after(new Date())) {
                    needSubscribe = false;
                    reason = "已订阅";
                } else {
                    // 已过期，更新状态
                    Db.update("UPDATE eh_wx_subscribe_record SET status = 0 WHERE id = ?",
                             subscribeRecord.getStr("id"));
                    reason = "订阅已过期";
                }
            }

            subscribeStatus.put("needSubscribe", needSubscribe);
            subscribeStatus.put("reason", reason);
            subscribeStatus.put("templateType", "property_notice");
            subscribeStatus.put("templateId", TEMPLATE_PROPERTY_NOTICE);

        } catch (Exception e) {
            logger.error("检查订阅状态失败: {}", e.getMessage(), e);
            subscribeStatus.put("needSubscribe", false);
            subscribeStatus.put("reason", "检查失败");
        }

        return subscribeStatus;
    }

    /**
     * 记录菜单点击日志
     */
    @RequestMapping("/recordMenuClick")
    public AjaxResult recordMenuClick() {
        try {
            JSONObject params = getParams();
            String menuId = params.getString("menuId");
            String menuName = params.getString("menuName");
            String menuType = params.getString("menuType");
            String source = params.getString("source");

            // 参数校验
            if (StringUtils.isEmpty(menuId) || StringUtils.isEmpty(menuName)) {
                return error("菜单ID和菜单名称不能为空");
            }

            // 获取当前用户信息
            LoginUser loginUser = getCurrentUser();
            if (loginUser == null) {
                return error("用户未登录");
            }

            Long userIdLong = loginUser.getUserId();
            String userId = userIdLong != null ? userIdLong.toString() : "";
            String userName = loginUser.getUsername();
            String ipAddress = IpUtils.getIpAddr(getRequest());

            // 获取业主和房屋信息
            String ownerId = "";
            String houseId = "";
            String houseName = "";
            String communityId = "";

            WxUser wxUser = wxUserService.selectWxUserById(userIdLong);
            if (wxUser != null) {
                ownerId = wxUser.getOwnerId();
                communityId = wxUser.getCommunityId();
                houseId = loginUser.getHouseId();
                houseName = loginUser.getHouseName();
            }

            // 异步记录菜单点击日志
            AsyncManager.me().execute(AsyncFactory.recordMenuClick(
                menuId, menuName, menuType, userId, userName,
                ipAddress, ownerId, houseId, houseName, communityId, source
            ));

            return success("记录成功");

        } catch (Exception e) {
            logger.error("记录菜单点击日志失败", e);
            return error("记录失败");
        }
    }

}
