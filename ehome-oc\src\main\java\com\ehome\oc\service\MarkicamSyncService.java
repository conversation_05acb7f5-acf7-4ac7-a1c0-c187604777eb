package com.ehome.oc.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.utils.DateUtils;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import java.util.concurrent.TimeUnit;

/**
 * Markicam数据同步服务
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
public class MarkicamSyncService {

    private static final Logger logger = LoggerFactory.getLogger(MarkicamSyncService.class);

    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private final OkHttpClient httpClient;

    public MarkicamSyncService() {
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }

    /**
     * 同步照片视频数据
     */
    public void syncMomentData(String communityId) {
        logger.info("开始同步照片视频数据，社区ID: {}", communityId);

        try {
            // 获取配置信息
            Record config = getConfig(communityId);
            if (config == null) {
                logger.error("未找到社区配置信息: {}", communityId);
                return;
            }

            // 获取时间范围
            String lastSyncTime = config.getStr("last_sync_time");
            String currentTime = DateUtils.getTime();

            // 调用按日期同步方法，设置合理的最大迭代次数
            int syncCount = syncMomentDataByDate(communityId, lastSyncTime, currentTime, 100);

            // 同步成功时更新最后同步时间
            if (syncCount >= 0) {
                updateLastSyncTime(communityId, currentTime);
                logger.info("照片视频数据同步完成，同步数量: {}", syncCount);
            } else {
                logger.error("照片视频数据同步失败，社区ID: {}", communityId);
            }

        } catch (Exception e) {
            logger.error("同步照片视频数据失败", e);
        }
    }

    /**
     * 按日期同步照片视频数据
     */
    public int syncMomentDataByDate(String communityId, String startTime, String endTime, int maxIterations) {
        logger.info("开始按日期同步照片视频数据，社区ID: {}, 开始时间: {}, 结束时间: {}, 最大遍历次数: {}",
            communityId, startTime, endTime, maxIterations);

        try {
            // 获取配置信息
            Record config = getConfig(communityId);
            if (config == null) {
                logger.error("未找到社区配置信息: {}", communityId);
                return 0;
            }

            int totalSyncCount = 0;
            int iteration = 0;
            boolean hasMore = true;
            String nextToken = null;

            while (hasMore && iteration < maxIterations) {
                iteration++;
                logger.info("第{}次遍历，next: {}", iteration, nextToken);

                // 调用API获取数据
                JSONObject params = new JSONObject();
                if (startTime != null && !startTime.isEmpty()) {
                    params.put("start", startTime);
                }
                params.put("end", endTime);
                if (nextToken != null && !nextToken.isEmpty()) {
                    params.put("next", nextToken);
                }

                JSONObject response = callMarkicamAPI("/marki/moment", params, config);
                if (response == null || response.getInteger("code") != 0) {
                    logger.error("第{}次调用照片视频API失败: {}", iteration, response);
                    break;
                }

                // 处理返回数据（兼容momList/momInfos）
                JSONObject data = response.getJSONObject("data");
                JSONArray momList = data.getJSONArray("momList");
                if (momList == null) {
                    momList = data.getJSONArray("momInfos");
                }
                if (momList == null) {
                    logger.warn("照片视频数据为空，结束");
                    break;
                }
                hasMore = data.getBooleanValue("hasMore");

                int batchSyncCount = 0;
                for (int i = 0; i < momList.size(); i++) {
                    JSONObject moment = momList.getJSONObject(i);
                    if (saveMomentData(moment, communityId)) {
                        batchSyncCount++;
                    }
                }

                totalSyncCount += batchSyncCount;
                logger.info("第{}次遍历完成，本次同步: {}, 累计同步: {}, hasMore: {}",
                    iteration, batchSyncCount, totalSyncCount, hasMore);

                nextToken = data.getString("next");
                if (momList.size() == 0) {
                    hasMore = false;
                }
            }

            logger.info("按日期同步照片视频数据完成，总遍历次数: {}, 总同步数量: {}", iteration, totalSyncCount);
            return totalSyncCount;

        } catch (Exception e) {
            logger.error("按日期同步照片视频数据失败", e);
            return 0;
        }
    }

    /**
     * 同步团队数据
     */
    public void syncTeamData(String communityId) {
        logger.info("开始同步团队数据，社区ID: {}", communityId);
        try {
            Record config = getConfig(communityId);
            if (config == null) {
                logger.error("未找到社区配置信息: {}", communityId);
                return;
            }

            // 官方文档：获取组织的团队列表 /marki/org/team
            JSONObject params = new JSONObject();
            JSONObject response = callMarkicamAPI("/marki/org/team", params, config);
            if (response == null || response.getInteger("code") != 0) {
                logger.error("调用团队列表API失败: {}", response);
                return;
            }

            JSONObject data = response.getJSONObject("data");
            // 文档字段：teamOrgList
            JSONArray teamList = data.getJSONArray("teamOrgList");
            int syncCount = 0;
            for (int i = 0; i < teamList.size(); i++) {
                JSONObject team = teamList.getJSONObject(i);
                if (saveTeamData(team, communityId)) {
                    syncCount++;
                }
            }
            logger.info("团队数据同步完成，同步数量: {}", syncCount);
        } catch (Exception e) {
            logger.error("同步团队数据失败", e);
        }
    }

    private boolean saveTeamData(JSONObject team, String communityId) {
        try {
            Integer teamId = team.getInteger("teamId");
            Record existing = Db.findFirst("SELECT id FROM eh_markicam_team WHERE community_id = ? AND team_id = ?",
                    communityId, teamId);
            Record record = new Record();
            if (existing != null) {
                record.set("id", existing.getLong("id"));
            }
            record.set("community_id", communityId);
            record.set("team_id", teamId);
            // 文档返回字段：teamId, createUID, manageUIDs[], createTime, parentTeam, OrganizeId
            // 由于团队名称/统计信息需从其他接口获取，这里仅保存基础字段
            record.set("team_name", record.getStr("team_name")); // 占位，不覆盖
            record.set("team_desc", record.getStr("team_desc"));
            record.set("member_count", record.getInt("member_count"));
            record.set("reg_member_count", record.getInt("reg_member_count"));
            record.set("unreg_member_count", record.getInt("unreg_member_count"));
            record.set("sync_time", DateUtils.getTime());
            if (existing != null) {
                record.set("updated_at", DateUtils.getTime());
                record.set("updated_by", "system");
                return Db.update("eh_markicam_team", record);
            } else {
                record.set("created_at", DateUtils.getTime());
                record.set("updated_at", DateUtils.getTime());
                record.set("created_by", "system");
                record.set("updated_by", "system");
                record.set("is_deleted", 0);
                record.set("is_active", 1);
                return Db.save("eh_markicam_team", record);
            }
        } catch (Exception e) {
            logger.error("保存团队数据失败", e);
            return false;
        }
    }

    /**
     * 同步成员数据
     */
    public void syncMemberData(String communityId, Integer teamId) {
        logger.info("开始同步成员数据，社区ID: {}, 团队ID: {}", communityId, teamId);

        try {
            Record config = getConfig(communityId);
            if (config == null) {
                logger.error("未找到社区配置信息: {}", communityId);
                return;
            }

            // 如果未指定团队ID，则遍历本地团队表逐个同步
            if (teamId == null) {
                List<Record> teams = Db.find(
                    "SELECT team_id FROM eh_markicam_team WHERE community_id = ? AND is_deleted = 0",
                    communityId
                );
                if (teams == null || teams.isEmpty()) {
                    logger.warn("本地未找到团队数据，社区ID: {}，请先同步团队数据", communityId);
                    return;
                }
                for (Record t : teams) {
                    Integer tid = t.getInt("team_id");
                    if (tid == null) { continue; }
                    logger.info("按团队同步成员数据，团队ID: {}", tid);
                    // 递归调用以重用单团队同步逻辑
                    syncMemberData(communityId, tid);
                }
                return;
            }

            String next = null;
            boolean hasMore = true;
            int syncCount = 0;

            while (hasMore) {
                JSONObject params = new JSONObject();
                params.put("teamId", teamId);
                if (next != null && !next.isEmpty()) {
                    params.put("next", next);
                }

                JSONObject response = callMarkicamAPI("/marki/team/mem", params, config);
                if (response == null || response.getInteger("code") != 0) {
                    logger.error("调用成员列表API失败: {}", response);
                    break;
                }

                JSONObject data = response.getJSONObject("data");
                if (data == null) { break; }

                // 第一页回填团队统计（regTotal/unRegTotal/total）
                if (next == null || next.isEmpty()) {
                    Integer regTotal = data.getInteger("regTotal");
                    Integer unRegTotal = data.getInteger("unRegTotal");
                    Integer total = data.getInteger("total");
                    if (regTotal != null || unRegTotal != null || total != null) {
                        int r = regTotal != null ? regTotal : 0;
                        int u = unRegTotal != null ? unRegTotal : 0;
                        int tt = total != null ? total : 0;
                        Db.update(
                            "UPDATE eh_markicam_team SET reg_member_count = ?, unreg_member_count = ?, member_count = ?, sync_time = ?, updated_at = ?, updated_by = ? WHERE community_id = ? AND team_id = ?",
                            r, u, tt, DateUtils.getTime(), DateUtils.getTime(), "system", communityId, teamId
                        );
                    }
                }

                JSONArray memberList = data.getJSONArray("memberList");
                if (memberList == null) { break; }

                for (int i = 0; i < memberList.size(); i++) {
                    JSONObject member = memberList.getJSONObject(i);
                    if (saveMemberData(member, communityId, teamId)) {
                        syncCount++;
                    }
                }

                next = data.getString("next");
                hasMore = data.getBooleanValue("hasMore");
                if (memberList.size() == 0) {
                    hasMore = false;
                }
            }

            logger.info("成员数据同步完成（团队ID: {}），同步数量: {}", teamId, syncCount);

        } catch (Exception e) {
            logger.error("同步成员数据失败", e);
        }
    }

    /**
     * 同步违规车辆数据
     */
    public void syncIllegalParkData(String communityId) {
        logger.info("开始同步违规车辆数据，社区ID: {}", communityId);

        try {
            Record config = getConfig(communityId);
            if (config == null) {
                logger.error("未找到社区配置信息: {}", communityId);
                return;
            }

            String lastSyncTime = config.getStr("last_sync_time");
            String currentTime = DateUtils.getTime();

            String next = null;
            boolean hasMore = true;
            int syncCount = 0;

            while (hasMore) {
                JSONObject params = new JSONObject();
                if (lastSyncTime != null && !lastSyncTime.isEmpty()) {
                    params.put("start", lastSyncTime);
                }
                params.put("end", currentTime);
                if (next != null && !next.isEmpty()) {
                    params.put("next", next);
                }

                JSONObject response = callMarkicamAPI("/marki/illegal_park", params, config);
                if (response == null || response.getInteger("code") != 0) {
                    logger.error("调用违规车辆API失败: {}", response);
                    break;
                }

                JSONObject data = response.getJSONObject("data");
                JSONArray carList = data.getJSONArray("carList");
                if (carList == null) {
                    logger.warn("违规车辆返回carList为空，结束");
                    break;
                }

                for (int i = 0; i < carList.size(); i++) {
                    JSONObject car = carList.getJSONObject(i);
                    if (saveIllegalParkData(car, communityId)) {
                        syncCount++;
                    }
                }

                next = data.getString("next");
                hasMore = data.getBooleanValue("hasMore");
                if (carList.size() == 0) {
                    hasMore = false;
                }
            }

            updateLastSyncTime(communityId, currentTime);

            logger.info("违规车辆数据同步完成，同步数量: {}", syncCount);

        } catch (Exception e) {
            logger.error("同步违规车辆数据失败", e);
        }
    }

    /**
     * 获取配置信息
     */
    private Record getConfig(String communityId) {
        return Db.findFirst(
            "SELECT * FROM eh_markicam_config WHERE community_id = ? AND is_enabled = 1 AND is_deleted = 0",
            communityId
        );
    }

    /**
     * 调用Markicam API
     */
    private JSONObject callMarkicamAPI(String endpoint, JSONObject params, Record config) {
        try {
            String orgId = config.getStr("org_id");
            String apiKey = config.getStr("api_key");
            String baseUrl = config.getStr("base_url");

            // 生成签名
            long timestamp = System.currentTimeMillis() / 1000;
            String traceId = "trace_" + timestamp;
            String sign = generateSign(orgId, apiKey, timestamp, traceId, params.toJSONString());

            // 构建请求
            String url = baseUrl + endpoint;
            RequestBody body = RequestBody.create(params.toJSONString(), JSON);

            Request request = new Request.Builder()
                    .url(url)
                    .post(body)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("orgId", orgId)
                    .addHeader("timestamp", String.valueOf(timestamp))
                    .addHeader("traceId", traceId)
                    .addHeader("sign", sign)
                    .build();

            // 发送请求
            try (Response response = httpClient.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("HTTP请求失败: {}", response.code());
                    return null;
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    logger.error("响应体为空");
                    return null;
                }

                String responseStr = responseBody.string();
                logger.debug("API响应: {}", responseStr);

                return JSONObject.parseObject(responseStr);
            }

        } catch (IOException e) {
            logger.error("调用Markicam API网络异常", e);
            return null;
        } catch (Exception e) {
            logger.error("调用Markicam API失败", e);
            return null;
        }
    }

    /**
     * 生成签名
     */
    private String generateSign(String orgId, String apiKey, long timestamp, String traceId, String data) {
        try {
            String signStr = String.format("orgId=%s&key=%s&timestamp=%d&traceId=%s&data=%s",
                orgId, apiKey, timestamp, traceId, data);

            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(signStr.getBytes("UTF-8"));

            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }

            return sb.toString();

        } catch (Exception e) {
            logger.error("生成签名失败", e);
            return "";
        }
    }

    /**
     * 保存照片视频数据
     */
    private boolean saveMomentData(JSONObject moment, String communityId) {
        try {
            // 检查是否已存在
            String markicamId = moment.getString("id");
            Record existing = Db.findFirst(
                "SELECT id FROM eh_markicam_moment WHERE markicam_id = ?", markicamId
            );

            if (existing != null) {
                return false; // 已存在，跳过
            }

            // 先保存或更新成员信息
            Integer uid = moment.getInteger("uid");
            Integer teamId = moment.getInteger("teamId");
            String nickname = moment.getString("nickname");

            if (uid != null && teamId != null) {
                saveMemberInfo(uid, teamId, nickname, communityId);
            }

            Record record = new Record();
            record.set("community_id", communityId);
            record.set("markicam_id", markicamId);
            record.set("uid", uid);
            record.set("team_id", teamId);
            record.set("url", moment.getString("url"));
            record.set("moment_type", moment.getInteger("momentType"));
            record.set("content", moment.getString("content"));
            record.set("mark_name", moment.getString("markName"));
            record.set("lng", moment.getBigDecimal("lng"));
            record.set("lat", moment.getBigDecimal("lat"));
            record.set("post_time", moment.getInteger("postTime"));
            record.set("post_time_str", timestampToString(moment.getInteger("postTime")));
            record.set("sync_time", DateUtils.getTime());
            record.set("created_at", DateUtils.getTime());
            record.set("updated_at", DateUtils.getTime());
            record.set("created_by", "system");
            record.set("updated_by", "system");
            record.set("is_deleted", 0);
            record.set("is_active", 1);
            record.set("is_public", 1);

            return Db.save("eh_markicam_moment", record);

        } catch (Exception e) {
            logger.error("保存照片视频数据失败", e);
            return false;
        }
    }

    /**
     * 保存或更新成员信息
     */
    private void saveMemberInfo(Integer uid, Integer teamId, String nickname, String communityId) {
        try {
            // 检查成员是否已存在
            Record existing = Db.findFirst(
                "SELECT id FROM eh_markicam_member WHERE community_id = ? AND uid = ? AND team_id = ?",
                communityId, uid, teamId
            );

            if (existing != null) {
                // 更新昵称
                if (nickname != null && !nickname.isEmpty()) {
                    Db.update(
                        "UPDATE eh_markicam_member SET nickname = ?, updated_at = ? WHERE id = ?",
                        nickname, DateUtils.getTime(), existing.getLong("id")
                    );
                }
            } else {
                // 新增成员记录
                Record record = new Record();
                record.set("community_id", communityId);
                record.set("uid", uid);
                record.set("team_id", teamId);
                record.set("nickname", nickname != null ? nickname : "");
                record.set("phone", "");
                record.set("join_time", 0);
                record.set("join_time_str", "");
                record.set("member_type", 3); // 默认为普通成员
                record.set("sync_time", DateUtils.getTime());
                record.set("created_at", DateUtils.getTime());
                record.set("updated_at", DateUtils.getTime());
                record.set("created_by", "system");
                record.set("updated_by", "system");
                record.set("is_deleted", 0);
                record.set("is_active", 1);

                Db.save("eh_markicam_member", record);
            }
        } catch (Exception e) {
            logger.error("保存成员信息失败", e);
        }
    }

    /**
     * 保存成员数据
     */
    private boolean saveMemberData(JSONObject member, String communityId, Integer teamId) {
        try {
            Integer uid = member.getInteger("uid");

            // 检查是否已存在
            Record existing = Db.findFirst(
                "SELECT id FROM eh_markicam_member WHERE community_id = ? AND uid = ? AND team_id = ?",
                communityId, uid, teamId
            );

            if (existing != null) {
                // 更新现有记录
                Record record = new Record();
                record.set("id", existing.getLong("id"));
                record.set("nickname", member.getString("nickname"));
                record.set("phone", member.getString("phone"));
                record.set("member_type", member.getInteger("memberType"));
                record.set("sync_time", DateUtils.getTime());
                record.set("updated_at", DateUtils.getTime());
                record.set("updated_by", "system");

                return Db.update("eh_markicam_member", record);
            } else {
                // 新增记录
                Record record = new Record();
                record.set("community_id", communityId);
                record.set("uid", uid);
                record.set("team_id", teamId);
                record.set("nickname", member.getString("nickname"));
                record.set("phone", member.getString("phone"));
                record.set("join_time", member.getInteger("joinTime"));
                record.set("join_time_str", timestampToString(member.getInteger("joinTime")));
                record.set("member_type", member.getInteger("memberType"));
                record.set("sync_time", DateUtils.getTime());
                record.set("created_at", DateUtils.getTime());
                record.set("updated_at", DateUtils.getTime());
                record.set("created_by", "system");
                record.set("updated_by", "system");
                record.set("is_deleted", 0);
                record.set("is_active", 1);

                return Db.save("eh_markicam_member", record);
            }

        } catch (Exception e) {
            logger.error("保存成员数据失败", e);
            return false;
        }
    }

    /**
     * 保存违规车辆数据
     */
    private boolean saveIllegalParkData(JSONObject car, String communityId) {
        try {
            Record record = new Record();
            record.set("community_id", communityId);
            record.set("team_id", car.getInteger("teamId"));
            record.set("report_uid", car.getInteger("reportUID"));
            record.set("car_plate", car.getString("carPlate"));
            record.set("car_picture", car.getString("carPicture"));
            record.set("report_time", car.getInteger("createTime"));
            record.set("report_time_str", timestampToString(car.getInteger("createTime")));
            record.set("sync_time", DateUtils.getTime());
            record.set("created_at", DateUtils.getTime());
            record.set("updated_at", DateUtils.getTime());
            record.set("created_by", "system");
            record.set("updated_by", "system");
            record.set("is_deleted", 0);
            record.set("is_active", 1);

            return Db.save("eh_markicam_illegal_park", record);

        } catch (Exception e) {
            logger.error("保存违规车辆数据失败", e);
            return false;
        }
    }

    /**
     * 更新最后同步时间
     */
    private void updateLastSyncTime(String communityId, String syncTime) {
        Db.update(
            "UPDATE eh_markicam_config SET last_sync_time = ?, updated_at = ? WHERE community_id = ?",
            syncTime, DateUtils.getTime(), communityId
        );
    }

    /**
     * 时间戳转字符串
     */
    private String timestampToString(Integer timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            Date date = new Date(timestamp * 1000L);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (Exception e) {
            return "";
        }
    }
}
