package com.ehome.oc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ehome.common.exception.ServiceException;
import com.ehome.common.utils.IpUtils;
import com.ehome.common.utils.StringUtils;
import com.ehome.oc.utils.WxCryptUtils;
import com.ehome.common.utils.http.HttpUtils;
import com.ehome.oc.domain.WxUser;
import com.ehome.oc.domain.dto.WxLoginDTO;
import com.ehome.oc.mapper.WxUserMapper;
import com.ehome.oc.service.IWxUserService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class WxUserServiceImpl implements IWxUserService {
    private static final Logger log = LoggerFactory.getLogger(WxUserServiceImpl.class);

    @Value("${wechat.appid}")
    private String appid;

    @Value("${wechat.secret}")
    private String secret;

    @Autowired
    private WxUserMapper wxUserMapper;

    @Autowired
    private HttpServletRequest request;

    @Override
    @Transactional
    public WxUser wxLogin(WxLoginDTO loginDTO) {
        try {
            String code = loginDTO.getCode();
            String openid = loginDTO.getOpenid();
            String unionid = loginDTO.getUnionid();
            String sessionKey = loginDTO.getSessionKey();

            // 如果提供了加密数据，尝试解密获取完整用户信息
            JSONObject decryptedUserInfo = null;
            if (StringUtils.isNotEmpty(loginDTO.getEncryptedData()) &&
                StringUtils.isNotEmpty(loginDTO.getIv()) &&
                StringUtils.isNotEmpty(sessionKey)) {
                try {
                    String decryptedData = WxCryptUtils.decrypt(sessionKey, loginDTO.getEncryptedData(), loginDTO.getIv());
                    decryptedUserInfo = JSON.parseObject(decryptedData);
                    log.info("成功解密用户信息: {}", decryptedUserInfo.toJSONString());
                } catch (Exception e) {
                    log.warn("解密用户信息失败，将使用默认信息: {}", e.getMessage());
                }
            }

            // 获取真实IP
            String ip = IpUtils.getIpAddr(request);

            // 查询用户是否存在
            WxUser wxUser = selectWxUserByOpenid(openid);
            boolean isFirstLogin = false;

            if (wxUser == null) {
                // 新用户，自动注册
                isFirstLogin = true;
                wxUser = new WxUser();
                wxUser.setOpenId(openid);
                wxUser.setUnionId(unionid);
                wxUser.setSessionKey(sessionKey);
                // 如果提供了手机号，直接设置
                if (StringUtils.isNotEmpty(loginDTO.getPhoneNumber())) {
                    wxUser.setMobile(loginDTO.getPhoneNumber());
                }
                // 使用解密后的用户信息或默认值
                if (decryptedUserInfo != null) {
                    wxUser.setNickName(decryptedUserInfo.getString("nickName"));
                    wxUser.setAvatarUrl(decryptedUserInfo.getString("avatarUrl"));
                    wxUser.setGender(String.valueOf(decryptedUserInfo.getIntValue("gender")));
                } else {
                    wxUser.setNickName("微信用户");
                }
                wxUser.setStatus("0");
                wxUser.setLoginIp(ip);
                wxUser.setLoginDate(new Date());
                wxUserMapper.insertWxUser(wxUser);
            } else {
                if (unionid != null && !unionid.equals(wxUser.getUnionId())) {
                    wxUser.setUnionId(unionid);
                }
                if (sessionKey != null && !sessionKey.equals(wxUser.getSessionKey())) {
                    wxUser.setSessionKey(sessionKey);
                }
                wxUser.setLoginIp(ip);
                wxUser.setLoginDate(new Date());
                wxUserMapper.updateWxUser(wxUser);
            }

            // 设置首次登录标识
            wxUser.setIsFirstLogin(isFirstLogin);

            Record wxLoginLog = new Record();
            wxLoginLog.set("user_id", wxUser.getUserId());
            wxLoginLog.set("mobile", wxUser.getMobile());
            wxLoginLog.set("login_ip", ip);
            wxLoginLog.set("login_date", new Date());
            wxLoginLog.set("user_agent", request.getHeader("User-Agent"));
            Db.save("wx_login_log","log_id", wxLoginLog);

            return wxUser;
        } catch (Exception e) {
            log.error("微信登录失败", e);
            throw new ServiceException("微信登录失败: " + e.getMessage());
        }
    }

    @Override
    public WxUser selectWxUserById(Long userId) {
        return wxUserMapper.selectWxUserById(userId);
    }

    @Override
    public int updateWxUser(WxUser user) {
        return wxUserMapper.updateWxUser(user);
    }

    @Override
    public WxUser selectWxUserByOpenid(String openid) {
        return wxUserMapper.selectWxUserByOpenid(openid);
    }

    /**
     * 根据code获取微信用户信息（openid、unionid、session_key）
     */
    private JSONObject getWxUserInfoByCode(String code) {
        if("0".equals(code)){
            JSONObject wxUserInfo = new JSONObject();
            wxUserInfo.put("openid", "0");
            wxUserInfo.put("unionid", "0");
            wxUserInfo.put("session_key", "0");
            return wxUserInfo;
        }

        try {
            // 微信登录凭证校验接口
            String url = String.format("https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code",
                    appid, secret, code);

            String response = HttpUtils.sendGet(url);
            JSONObject jsonObject = JSON.parseObject(response);

            // 判断是否成功
            if (jsonObject.containsKey("errcode") && jsonObject.getIntValue("errcode") != 0) {
                log.error("获取微信用户信息失败: {}", response);
                return null;
            }

            return jsonObject;
        } catch (Exception e) {
            log.error("调用微信接口失败", e);
            return null;
        }
    }

    @Override
    public void updateUserLoginInfo(Long userId, String loginIp) {
        try {
            WxUser wxUser = new WxUser();
            wxUser.setUserId(userId);
            wxUser.setLoginIp(loginIp);
            wxUser.setLoginDate(new Date());
            wxUserMapper.updateWxUser(wxUser);
            log.info("更新用户登录信息成功，用户ID: {}, 登录IP: {}", userId, loginIp);
        } catch (Exception e) {
            log.error("更新用户登录信息失败，用户ID: {}, 错误: {}", userId, e.getMessage(), e);
        }
    }

    @Override
    public WxUser checkUserByCode(String code) {
        try {
            // 调用微信接口获取用户信息
            JSONObject wxUserInfo = getWxUserInfoByCode(code);
            if (wxUserInfo == null) {
                log.warn("获取微信用户信息失败，code: {}", code);
                return null;
            }

            String openid = wxUserInfo.getString("openid");
            if (StringUtils.isEmpty(openid)) {
                log.warn("微信返回的openid为空，code: {}", code);
                return null;
            }

            // 查询用户是否存在
            WxUser wxUser = selectWxUserByOpenid(openid);
            log.info("检查用户是否存在: openid={}, 用户存在={}", openid, wxUser != null);

            return wxUser;
        } catch (Exception e) {
            log.error("检查用户失败，code: {}, 错误: {}", code, e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> checkUserWithWxInfo(String code) {
        try {
            // 调用微信接口获取用户信息
            JSONObject wxUserInfo = getWxUserInfoByCode(code);
            if (wxUserInfo == null) {
                log.warn("获取微信用户信息失败，code: {}", code);
                throw new RuntimeException("获取微信用户信息失败");
            }

            String openid = wxUserInfo.getString("openid");
            String unionid = wxUserInfo.getString("unionid");
            String sessionKey = wxUserInfo.getString("session_key");

            if (StringUtils.isEmpty(openid)) {
                log.warn("微信返回的openid为空，code: {}", code);
                throw new RuntimeException("微信返回的openid为空");
            }

            // 查询用户是否存在
            WxUser existingUser = selectWxUserByOpenid(openid);
            boolean userExists = existingUser != null;

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("userExists", userExists);
            result.put("openid", openid);
            result.put("unionid", unionid);
            result.put("sessionKey", sessionKey);

            if (userExists) {
                result.put("userId", existingUser.getUserId());
                result.put("nickName", existingUser.getNickName());
                result.put("avatarUrl", existingUser.getAvatarUrl());
                result.put("mobile", existingUser.getMobile());
            }

            log.info("检查用户完成: openid={}, 用户存在={}", openid, userExists);
            return result;

        } catch (Exception e) {
            log.error("检查用户失败，code: {}, 错误: {}", code, e.getMessage(), e);
            throw new RuntimeException("检查用户失败: " + e.getMessage(), e);
        }
    }

    @Override
    public WxUser selectWxUserByMobile(String mobile) {
        if (StringUtils.isEmpty(mobile)) {
            return null;
        }
        try {
            Record record = Db.findFirst("select * from eh_wx_user where mobile = ? and status = '0'", mobile);
            if (record != null) {
                WxUser wxUser = new WxUser();
                wxUser.setUserId(record.getLong("user_id"));
                wxUser.setOpenId(record.getStr("openid"));
                wxUser.setUnionId(record.getStr("unionid"));
                wxUser.setNickName(record.getStr("nick_name"));
                wxUser.setMobile(record.getStr("mobile"));
                wxUser.setCommunityId(record.getStr("community_id"));
                wxUser.setOwnerId(record.getStr("owner_id"));
                wxUser.setUserRoles(record.getStr("user_roles"));
                return wxUser;
            }
            return null;
        } catch (Exception e) {
            log.error("根据手机号查询微信用户失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public Map<String, Object> checkUserIdentityByPhone(String phoneNumber) {
        return checkUserIdentityByPhoneWithCache(phoneNumber, null);
    }

    @Override
    public Map<String, Object> checkUserIdentityByPhoneWithCache(String phoneNumber, WxUser wxUser) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 如果没有传入wxUser，先查询
            if (wxUser == null) {
                wxUser = selectWxUserByMobile(phoneNumber);
            }

            // 检查是否已有角色缓存
            if (wxUser != null && wxUser.getUserRoles() != null && !wxUser.getUserRoles().isEmpty()) {
                boolean hasOwnerIdentity = wxUser.hasRole("owner");
                boolean hasPropertyIdentity = wxUser.hasRole("property");

                result.put("hasOwnerIdentity", hasOwnerIdentity);
                result.put("hasPropertyIdentity", hasPropertyIdentity);

                // 查询详细信息
                result.put("ownerUser", hasOwnerIdentity ? getOwnerInfo(wxUser,phoneNumber) : null);
                result.put("propertyUser", hasPropertyIdentity ? getPropertyInfo(phoneNumber) : null);

                log.info("使用缓存的身份信息: 手机号={}, 角色={}", phoneNumber, wxUser.getUserRoles());
                return result;
            }

            // 没有缓存，进行完整查询
            Record ownerRecord = Db.findFirst(
                "select owner_id, owner_name, mobile, community_id from eh_owner where mobile = ?",
                phoneNumber
            );

            Record propertyRecord = Db.findFirst(
                "select user_id, login_name, user_name, phonenumber, community_id, dept_id, status " +
                "from sys_user where phonenumber = ? and status = '0' and del_flag = '0'",
                phoneNumber
            );

            boolean hasOwnerIdentity = ownerRecord != null;
            boolean hasPropertyIdentity = propertyRecord != null;

            // 更新角色缓存
            if (wxUser != null && (hasOwnerIdentity || hasPropertyIdentity)) {
                String roles = "";
                if (hasOwnerIdentity) roles = "owner";
                if (hasPropertyIdentity) roles = roles.isEmpty() ? "property" : roles + ",property";

                wxUser.setUserRoles(roles);
                updateWxUser(wxUser);
                log.info("更新用户角色缓存: 手机号={}, 角色={}", phoneNumber, roles);
            }

            result.put("hasOwnerIdentity", hasOwnerIdentity);
            result.put("hasPropertyIdentity", hasPropertyIdentity);
            result.put("ownerUser", hasOwnerIdentity ? buildOwnerInfo(ownerRecord) : null);
            result.put("propertyUser", hasPropertyIdentity ? buildPropertyInfo(propertyRecord) : null);

            log.info("身份识别完成: 手机号={}, 业主身份={}, 物业身份={}",
                    phoneNumber, hasOwnerIdentity, hasPropertyIdentity);

        } catch (Exception e) {
            log.error("身份识别失败: 手机号={}, 错误={}", phoneNumber, e.getMessage(), e);
            result.put("hasOwnerIdentity", false);
            result.put("hasPropertyIdentity", false);
            result.put("ownerUser", null);
            result.put("propertyUser", null);
        }

        return result;
    }

    private Map<String, Object> getOwnerInfo(WxUser wxUser,String phoneNumber) {
        Record ownerRecord = null;
        if(wxUser!=null&&StringUtils.isNotEmpty(wxUser.getOwnerId())){
            ownerRecord = Db.findFirst(
                "select owner_id, owner_name, mobile, community_id from eh_owner where owner_id = ?",
                wxUser.getOwnerId()
            );
        }
        if(ownerRecord==null){
            ownerRecord = Db.findFirst(
                    "select owner_id, owner_name, mobile, community_id from eh_owner where mobile = ?",
                    phoneNumber
            );
        }
        return ownerRecord != null ? buildOwnerInfo(ownerRecord) : null;
    }

    private Map<String, Object> getPropertyInfo(String phoneNumber) {
        Record propertyRecord = Db.findFirst(
            "select user_id, login_name, user_name, phonenumber, community_id, dept_id, status " +
            "from sys_user where phonenumber = ? and status = '0' and del_flag = '0'",
            phoneNumber
        );
        return propertyRecord != null ? buildPropertyInfo(propertyRecord) : null;
    }

    private Map<String, Object> buildOwnerInfo(Record ownerRecord) {
        Map<String, Object> ownerUser = new HashMap<>();
        ownerUser.put("ownerId", ownerRecord.getStr("owner_id"));
        ownerUser.put("ownerName", ownerRecord.getStr("owner_name"));
        ownerUser.put("mobile", ownerRecord.getStr("mobile"));
        ownerUser.put("communityId", ownerRecord.getStr("community_id"));
        return ownerUser;
    }

    private Map<String, Object> buildPropertyInfo(Record propertyRecord) {
        Map<String, Object> propertyUser = new HashMap<>();
        propertyUser.put("userId", propertyRecord.getLong("user_id"));
        propertyUser.put("loginName", propertyRecord.getStr("login_name"));
        propertyUser.put("userName", propertyRecord.getStr("user_name"));
        propertyUser.put("phonenumber", propertyRecord.getStr("phonenumber"));
        propertyUser.put("communityId", propertyRecord.getStr("community_id"));
        propertyUser.put("deptId", propertyRecord.getLong("dept_id"));
        return propertyUser;
    }

}