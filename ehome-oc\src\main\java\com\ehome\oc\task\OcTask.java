package com.ehome.oc.task;


import com.ehome.oc.service.CCBJobService;
import com.ehome.oc.service.MarkicamSyncService;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("ocTask")
public class OcTask {

    private static final Logger logger = LoggerFactory.getLogger(OcTask.class);

    @Autowired
    private MarkicamSyncService markicamService;

    /**
     * 定时同步所有小区的Markicam数据
     */
    public void synMarkicamData() {
        logger.info("开始定时同步所有小区的Markicam数据");

        try {
            // 获取所有启用的配置
            List<Record> configs = Db.find(
                "SELECT community_id FROM eh_markicam_config WHERE is_enabled = 1 AND is_deleted = 0"
            );

            if (configs == null || configs.isEmpty()) {
                logger.warn("未找到启用的Markicam配置，跳过同步");
                return;
            }

            logger.info("找到{}个启用的Markicam配置", configs.size());

            int successCount = 0;
            int failCount = 0;

            for (Record config : configs) {
                String communityId = config.getStr("community_id");
                try {
                    logger.info("开始同步小区 {} 的Markicam数据", communityId);
                    markicamService.syncMomentData(communityId);
                    successCount++;
                    logger.info("小区 {} Markicam数据同步成功", communityId);
                } catch (Exception e) {
                    failCount++;
                    logger.error("小区 {} Markicam数据同步失败: {}", communityId, e.getMessage(), e);
                }
            }

            logger.info("定时同步Markicam数据完成，总计: {}, 成功: {}, 失败: {}",
                configs.size(), successCount, failCount);

        } catch (Exception e) {
            logger.error("定时同步Markicam数据失败", e);
        }
    }

    public void synCardTransDetailList(){
        CCBJobService ccbJobService = new CCBJobService();
        ccbJobService.syncBalance();
        ccbJobService.synCardTransDetailList();
    }
}
