<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('小程序设置')" />
	<th:block th:include="include :: bootstrap-fileinput-css" />
    <style>
        /* 页面整体背景 */
        body {
            background-color: #f8f8f8 !important;
        }

        /* 主容器样式 */
        .wrapper.wrapper-content {
            background-color: #f8f8f8;
            padding: 0px;
            padding-bottom: 100px; /* 为固定按钮留出空间 */
        }

        /* 卡片容器 */
        .content-card {
            background-color: #fff;
            border-radius: 6px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }

        /* 卡片标题 */
        .card-header {
            background-color: #fff;
            padding: 20px 25px 15px;
            border-bottom: 1px solid #e7eaec;
        }

        .card-header h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        /* 卡片内容 */
        .card-body {
            padding: 25px;
        }

        /* 固定底部按钮 */
        .fixed-bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background-color: #fff;
            padding: 15px;
            border-top: 1px solid #e7eaec;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            text-align: center;
        }

        .fixed-bottom-buttons .btn {
            margin: 0 8px;
            padding: 8px 20px;
        }

        /* 表单样式优化 */
        .form-control {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 12px;
            height: auto;
            min-height: 34px;
            line-height: 1.42857143;
        }

        .form-control:focus {
            border-color: #5cb85c;
            box-shadow: 0 0 0 2px rgba(92, 184, 92, 0.2);
        }

        /* 下拉框特殊样式 */
        select.form-control {
            height: 34px;
            padding: 6px 12px;
            line-height: 1.42857143;
        }

        /* 确保下拉框选项文本完整显示 */
        select.form-control option {
            padding: 4px 8px;
            line-height: normal;
        }

        /* 按钮样式优化 */
        .btn {
            border-radius: 4px;
            font-weight: 500;
        }

        /* 修复可能的样式冲突 */
        .form-group label {
            font-weight: normal;
            margin-bottom: 5px;
        }

        /* 确保表单控件有足够的空间 */
        .form-horizontal .form-group {
            margin-left: 0;
            margin-right: 0;
        }

        /* 小提示文本样式 */
        .form-text.text-muted {
            font-size: 12px;
            color: #777;
            margin-top: 5px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <form class="form-horizontal m" id="form-maintain" name="form-maintain">
            <input name="base.oc_id" th:value="${ocId}" type="hidden">
            <input name="oc_id" th:value="${ocId}" type="hidden">

            <!-- 小程序首页标题 -->
            <div class="content-card">
                <div class="card-header">
                    <h4>小程序首页标题</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">标题：</label>
                                <div class="col-sm-8">
                                    <input name="ext.miniprogram_title" class="form-control" type="text" placeholder="零融金物业">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">是否显示：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_miniprogram_title" class="form-control">
                                        <option value="1">是</option>
                                        <option value="0">否</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">副标题：</label>
                                <div class="col-sm-8">
                                    <input name="ext.miniprogram_subtitle" class="form-control" type="text" placeholder="业主自管，公开、透明、可监督！">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">小程序logo：</label>
                                <div class="col-sm-8">
                                    <!-- 隐藏的输入框存储图片URL -->
                                    <input id="miniprogram_logo" name="ext.miniprogram_logo" type="hidden">
                                    <!-- 文件上传组件 -->
                                    <input id="miniprogram_logo_upload" name="file" type="file" class="file-upload">
                                    <!-- 图片预览区域 -->
                                    <div id="miniprogram_logo_preview" style="margin-top: 10px; display: none;">
                                        <img id="miniprogram_logo_img" src="" alt="小程序logo预览" style="max-width: 200px; max-height: 100px; border: 1px solid #ddd; border-radius: 4px;">
                                        <br>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="removeLogo()" style="margin-top: 5px;">
                                            <i class="fa fa-trash"></i> 删除图片
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 首页显示设置 -->
            <div class="content-card">
                <div class="card-header">
                    <h4>首页显示设置</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">置顶大菜单：</label>
                                <div class="col-sm-8">
                                    <select name="ext.show_big_card" class="form-control">
                                        <option value="1">显示</option>
                                        <option value="0">隐藏</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">菜单行显示个数：</label>
                                <div class="col-sm-8">
                                    <select name="ext.menu_rows" class="form-control">
                                        <option value="4">4行</option>
                                        <option value="3">3行</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h4>导航栏菜单</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">马克相机：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_markicam" class="form-control">
                                        <option value="0">关闭</option>
                                        <option value="1">开启</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">财务公开：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_financial" class="form-control">
                                        <option value="0">关闭</option>
                                        <option value="1">开始</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
                <!-- 安全设置 -->
                <div class="content-card">
                    <div class="card-header">
                        <h4>安全设置</h4>
                    </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">小程序水印：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_miniprogram_mark" class="form-control">
                                        <option value="0">关闭</option>
                                        <option value="1">开启</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">是否可以截屏：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_screenshot" class="form-control">
                                        <option value="1">允许</option>
                                        <option value="0">禁止</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分享设置 -->
            <div class="content-card">
                <div class="card-header">
                    <h4>分享设置</h4>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">是否开启分享功能：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_share" class="form-control">
                                        <option value="1">开启</option>
                                        <option value="0">关闭</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">分享图片：</label>
                                <div class="col-sm-8">
                                    <select name="ext.share_image" class="form-control">
                                        <option value="/static/images/share-cover.png">默认分享图片</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">分享标题：</label>
                                <div class="col-sm-8">
                                    <input type="text" name="ext.share_title" class="form-control"
                                           placeholder="智慧社区服务" maxlength="50">
                                    <small class="form-text text-muted">系统会自动在前面加上小区名称</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">分享描述：</label>
                                <div class="col-sm-8">
                                    <input type="text" name="ext.share_desc" class="form-control"
                                           placeholder="一键触达生活所需，物业服务就在掌心" maxlength="100">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">启用分享统计：</label>
                                <div class="col-sm-8">
                                    <select name="ext.enable_share_statistics" class="form-control">
                                        <option value="1">启用</option>
                                        <option value="0">禁用</option>
                                    </select>
                                    <small class="form-text text-muted">记录分享行为和访问数据</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">显示分享者信息：</label>
                                <div class="col-sm-8">
                                    <select name="ext.show_sharer_info" class="form-control">
                                        <option value="1">显示</option>
                                        <option value="0">不显示</option>
                                    </select>
                                    <small class="form-text text-muted">在分享内容中显示推荐人信息</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>

    <!-- 固定在底部的提交按钮 -->
    <div class="fixed-bottom-buttons">
        <button type="button" class="btn btn-primary" onclick="submitHandler()">
            <i class="fa fa-save"></i> 提交保存
        </button>
        <button type="button" class="btn btn-default" onclick="closeItem()">
            <i class="fa fa-reply-all"></i> 关闭
        </button>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-fileinput-js" />
    <script type="text/javascript">
	    var prefix = ctx + "oc/info";
	
	    $("#form-maintain").validate({
	    	onkeyup: false,
	        rules: {},
	        messages: {},
	        focusCleanup: true
	    });

        $(function(){
            // 初始化logo上传组件
            initLogoUpload();

            // 只加载扩展字段数据，不加载基础字段
            $.post(prefix + '/record', {oc_id: $("input[name='oc_id']").val()}, function(rs){
                if(rs.code === 0 && rs.data && rs.data.ext_json){
                    var extJson = JSON.parse(rs.data.ext_json);
                    fillRecord(extJson, 'ext.','');
                    // 处理logo图片回填
                    if(extJson.miniprogram_logo){
                        showLogoPreview(extJson.miniprogram_logo);
                    }
                }
            });
        });

        // 初始化logo上传组件
        function initLogoUpload() {
            $("#miniprogram_logo_upload").fileinput({
                uploadUrl: ctx + 'common/upload',
                maxFileCount: 1,
                autoReplace: true,
                allowedFileExtensions: ['jpg', 'jpeg', 'png', 'gif', 'bmp'],
                maxFileSize: 5120, // 5MB
                language: 'zh',
                dropZoneEnabled: false,
                dragDropEnabled: false,
                uploadAsync: true,
                showUpload: true,  // 显示上传按钮
                showRemove: true,
                uploadExtraData: {
                    bucketType: 'public',  // 公有参考
                    source: 'logo'         // 来源是logo
                }
            }).on('fileuploaded', function(event, data, previewId, index) {
                console.log('Upload response:', data.response);
                var response = data.response;
                if (response && response.code === 0) {
                    // 上传成功，保存URL到隐藏输入框
                    $("#miniprogram_logo").val(response.url);
                    showLogoPreview(response.url);
                    $.modal.msgSuccess("logo上传成功");
                } else {
                    console.error('Upload failed:', response);
                    $.modal.msgError(response ? (response.msg || "上传失败") : "上传响应异常");
                }
            }).on('fileuploaderror', function(event, data, msg) {
                console.error('Upload error:', msg, data);
                $.modal.msgError("上传出错: " + msg);
            }).on('fileremoved', function(event, id, index) {
                // 文件被删除时清空URL
                $("#miniprogram_logo").val('');
                hideLogoPreview();
            }).on('fileclear', function(event) {
                // 清空所有文件时清空URL
                $("#miniprogram_logo").val('');
                hideLogoPreview();
            });
        }

        // 显示logo预览
        function showLogoPreview(url) {
            $("#miniprogram_logo_img").attr('src', url);
            $("#miniprogram_logo_preview").show();
            $("#miniprogram_logo_upload").hide();
        }

        // 隐藏logo预览
        function hideLogoPreview() {
            $("#miniprogram_logo_preview").hide();
            $("#miniprogram_logo_upload").show();
        }

        // 删除logo
        function removeLogo() {
            $("#miniprogram_logo").val('');
            hideLogoPreview();
            // 清空fileinput组件
            $("#miniprogram_logo_upload").fileinput('clear');
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/maintainSave", $('#form-maintain').serialize(),function(){
                    layer.msg('保存成功');
                });
            }
        }

        function closeItem() {
            var index = parent.layer.getFrameIndex(window.name);
            parent.layer.close(index);
        }
    </script>
</body>
</html>
