<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('照片视频管理')" />
    <style>
        .moment-image {
            max-width: 100px;
            max-height: 60px;
            cursor: pointer;
        }
        .moment-content {
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>团队：</label>
                                <select name="team_id" id="teamSelect"><option value="">全部团队</option></select>
                            </li>
                            <li>
                                <label>用户：</label>
                                <select name="user_id" id="userSelect"><option value="">全部用户</option></select>
                            </li>
                            <li>
                                <label>类型：</label>
                                <select name="moment_type">
                                    <option value="">所有</option>
                                    <option value="1">照片</option>
                                    <option value="2">视频</option>
                                </select>
                            </li>
                            <li class="select-time">
                                <label>上传时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="start_time"/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="end_time"/>
                            </li>
                            <li>
                                <label>是否公示：</label>
                                <select name="is_public">
                                    <option value="">全部</option>
                                    <option value="1">已公示</option>
                                    <option value="0">未公示</option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="syncMomentData()" shiro:hasPermission="oc:markicam:sync">
                    <i class="fa fa-refresh"></i> 同步照片视频
                </a>
                <a class="btn btn-primary" onclick="syncMomentByDate()" shiro:hasPermission="oc:markicam:sync">
                    <i class="fa fa-calendar"></i> 按时间同步
                </a>
                <a class="btn btn-info" onclick="viewDetails()" shiro:hasPermission="oc:markicam:view">
                    <i class="fa fa-eye"></i> 查看详情
                </a>
                <a class="btn btn-success" onclick="batchSetPublic(1)" shiro:hasPermission="oc:markicam:edit">
                    <i class="fa fa-check"></i> 批量公示
                </a>
                <a class="btn btn-warning" onclick="batchSetPublic(0)" shiro:hasPermission="oc:markicam:edit">
                    <i class="fa fa-times"></i> 取消公示
                </a>
                <a class="btn btn-warning" onclick="exportData()" shiro:hasPermission="oc:markicam:export">
                    <i class="fa fa-download"></i> 导出数据
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <!-- 按日期同步模态框 -->
    <div class="modal fade" id="syncDateModal" tabindex="-1" role="dialog" aria-labelledby="syncDateModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="syncDateModalLabel">按时间范围同步照片视频</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="syncDateForm">
                        <div class="form-group">
                            <label for="syncStartDate">开始时间 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="syncStartDate" name="start_date" required placeholder="2020-01-20 00:00:00">
                        </div>
                        <div class="form-group">
                            <label for="syncEndDate">结束时间 <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="syncEndDate" name="end_date" required placeholder="2020-01-20 23:59:59">
                        </div>
                        <div class="form-group">
                            <label for="maxIterations">最大遍历次数</label>
                            <input type="number" class="form-control" id="maxIterations" name="max_iterations" value="10" min="1" max="50">
                            <small class="form-text text-muted">根据hasMore字段最多遍历的次数，默认10次</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="doSyncByDate()">开始同步</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailModal" tabindex="-1" role="dialog" aria-labelledby="detailModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="detailModalLabel">照片视频详情</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="detailContent">
                    <!-- 详情内容将在这里动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        var prefix = ctx + "oc/markicam";

        $(function() {
            var options = {
                url: prefix + "/moment/list",
                createUrl: prefix + "/moment/add",
                updateUrl: prefix + "/moment/edit/{id}",
                removeUrl: prefix + "/moment/remove",
                exportUrl: prefix + "/moment/export",
                modalName: "照片视频",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'id',
                    title: 'ID',
                    visible: false
                },
                {
                    field: 'markicam_id',
                    title: 'Markicam ID',
                    width: 120
                },
                {
                    field: 'team_id',
                    title: '团队ID',
                    width: 100
                },
                {
                    field: 'team_name',
                    title: '团队名称',
                    width: 120,
                    visible:  false
                },
                {
                    field: 'uid',
                    title: '用户ID',
                    width: 80,
                    visible:  false
                },
                {
                    field: 'nickname',
                    title: '拍摄者',
                    width: 100,
                    formatter: function(value, row, index) {
                        return value || '用户' + row.uid;
                    }
                },
                {
                    field: 'moment_type',
                    title: '类型',
                    align: 'center',
                    width: 60,
                    formatter: function(value, row, index) {
                        if (value == 1) {
                            return '<span class="badge badge-primary">照片</span>';
                        } else if (value == 2) {
                            return '<span class="badge badge-info">视频</span>';
                        }
                        return value;
                    }
                },
                {
                    field: 'url',
                    title: '预览',
                    align: 'center',
                    width: 120,
                    formatter: function(value, row, index) {
                        if (value) {
                            if (row.moment_type == 1) {
                                return '<img src="' + value + '" class="moment-image" onclick="previewImage(\'' + value + '\')" alt="照片">';
                            } else {
                                return '<a href="' + value + '" target="_blank" class="btn btn-xs btn-info"><i class="fa fa-play"></i> 播放</a>';
                            }
                        }
                        return '-';
                    }
                },
                {
                    field: 'mark_name',
                    title: '水印名称',
                    width: 120
                },
                {
                    field: 'content',
                    title: '水印内容',
                    width: 200,
                    formatter: function(value, row, index) {
                        if (value && value.length > 50) {
                            return '<span class="moment-content" style="cursor: pointer; color: #007bff;" onclick="showContentDetail(\'' + row.id + '\', \'' + encodeURIComponent(value) + '\')" title="点击查看详情">' + value.substring(0, 50) + '...</span>';
                        }
                        return value || '-';
                    }
                },
                {
                    field: 'is_public',
                    title: '是否公示',
                    align: 'center',
                    width: 80,
                    formatter: function(value, row, index) {
                        var checked = value == 1 ? 'checked' : '';
                        return '<input type="checkbox" ' + checked + ' onchange="togglePublic(' + row.id + ', this.checked)" />';
                    }
                },
                {
                    field: 'lng',
                    title: '经度',
                    width: 80,
                    visible:  false
                },
                {
                    field: 'lat',
                    title: '纬度',
                    width: 80,
                    visible:  false
                },
                {
                    field: 'post_time_str',
                    title: '上传时间',
                    width: 150
                },
                {
                    field: 'sync_time',
                    title: '同步时间',
                    width: 150
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 80,
                    formatter: function(value, row, index) {
                        return '<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="viewDetail(\'' + row.id + '\')"><i class="fa fa-eye"></i>详情</a>';
                    }
                }]
            };
            $.table.init(options);

            layui.use('laydate', function() {
                var laydate = layui.laydate;
                // 初始化时间选择器
                laydate.render({
                    elem: '#startTime',
                    type: 'datetime'
                });
                laydate.render({
                    elem: '#endTime',
                    type: 'datetime'
                });
                laydate.render({
                    elem: '#syncStartDate',
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss'
                });
                laydate.render({
                    elem: '#syncEndDate',
                    type: 'datetime',
                    format: 'yyyy-MM-dd HH:mm:ss'
                });

                // 加载团队下拉
                $.post(prefix + "/team/list", { pageNum: 1, pageSize: 1000 }, function(res){
                    if (res.total && res.rows) {
                        var opts = ['<option value="">全部团队</option>'];
                        res.rows.forEach(function(r){ opts.push('<option value="'+ r.team_id +'">'+ (r.team_name || r.team_id) +'</option>'); });
                        $('#teamSelect').html(opts.join(''));

                        // URL参数回填
                        var params = new URLSearchParams(window.location.search);
                        if (params.has('team_id')) {
                            $("select[name='team_id']").val(params.get('team_id'));
                            $.table.search();
                        }
                    }
                });
            });
        });

        function syncMomentData() {
            $.modal.confirm("确定要同步照片视频数据吗？", function() {
                $.modal.loading("正在同步数据...");
                $.post(prefix + "/sync/moment", {}, function(result) {
                    $.modal.closeLoading();
                    if (result.code == 0) {
                        $.modal.alertSuccess("同步成功");
                        $.table.refresh();
                    } else {
                        $.modal.alertError("同步失败：" + result.msg);
                    }
                }).fail(function() {
                    $.modal.closeLoading();
                    $.modal.alertError("同步失败");
                });
            });
        }

        function syncMomentByDate() {
            $('#syncDateForm')[0].reset();
            $('#maxIterations').val(10);
            $('#syncDateModal').modal('show');
        }

        function doSyncByDate() {
            var startDate = $('#syncStartDate').val();
            var endDate = $('#syncEndDate').val();
            var maxIterations = $('#maxIterations').val();

            if (!startDate || !endDate) {
                $.modal.alertWarning("请选择开始和结束时间");
                return;
            }

            // 验证日期时间格式 YYYY-MM-DD HH:mm:ss
            var dateTimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
            if (!dateTimeRegex.test(startDate) || !dateTimeRegex.test(endDate)) {
                $.modal.alertWarning("请使用正确的时间格式：YYYY-MM-DD HH:mm:ss");
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                $.modal.alertWarning("开始时间不能大于结束时间");
                return;
            }

            $('#syncDateModal').modal('hide');
            $.modal.loading("正在按日期同步数据...");

            $.post(prefix + "/sync/momentByDate", {
                start: startDate,
                end: endDate,
                max_iterations: maxIterations
            }, function(result) {
                $.modal.closeLoading();
                if (result.code == 0) {
                    $.modal.alertSuccess("同步成功，共同步 " + (result.data.syncCount || 0) + " 条数据");
                    $.table.refresh();
                } else {
                    $.modal.alertError("同步失败：" + result.msg);
                }
            }).fail(function() {
                $.modal.closeLoading();
                $.modal.alertError("同步失败");
            });
        }

        function viewDetails() {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请选择一条记录");
                return;
            }
            viewDetail(rows[0].id);
        }

        function viewDetail(id) {
            $.get(prefix + "/moment/detail/" + id, function(result) {
                if (result.code == 0) {
                    var data = result.data;
                    var content = '<div class="row">';
                    content += '<div class="col-md-6">';
                    content += '<p><strong>Markicam ID:</strong> ' + (data.markicam_id || '-') + '</p>';
                    content += '<p><strong>团队ID:</strong> ' + (data.team_id || '-') + '</p>';
                    content += '<p><strong>用户ID:</strong> ' + (data.uid || '-') + '</p>';
                    content += '<p><strong>类型:</strong> ' + (data.moment_type == 1 ? '照片' : '视频') + '</p>';
                    content += '<p><strong>水印名称:</strong> ' + (data.mark_name || '-') + '</p>';
                    content += '<p><strong>经纬度:</strong> ' + (data.lng || '-') + ', ' + (data.lat || '-') + '</p>';
                    content += '</div>';
                    content += '<div class="col-md-6">';
                    content += '<p><strong>上传时间:</strong> ' + (data.post_time_str || '-') + '</p>';
                    content += '<p><strong>同步时间:</strong> ' + (data.sync_time || '-') + '</p>';
                    if (data.url) {
                        content += '<p><strong>文件链接:</strong> <a href="' + data.url + '" target="_blank">查看原文件</a></p>';
                    }
                    content += '</div>';
                    content += '</div>';
                    if (data.content) {
                        content += '<div class="row"><div class="col-md-12">';
                        content += '<p><strong>水印内容:</strong></p>';
                        content += '<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px;">' + data.content + '</pre>';
                        content += '</div></div>';
                    }

                    $('#detailContent').html(content);
                    $('#detailModal').modal('show');
                } else {
                    $.modal.alertError(result.msg);
                }
            });
        }

        function previewImage(url) {
            var content = '<div class="text-center"><img src="' + url + '" style="max-width: 100%; max-height: 500px;" alt="照片预览"></div>';
            $('#detailContent').html(content);
            $('#detailModalLabel').text('照片预览');
            $('#detailModal').modal('show');
        }

        function exportData() {
            $.modal.confirm("确定要导出照片视频数据吗？", function() {
                var formData = $("#formId").serialize();
                window.location.href = prefix + "/moment/export?" + formData;
            });
        }

        // 切换单条记录的公示状态
        function togglePublic(id, isPublic) {
            $.post(prefix + "/moment/setPublic", {
                id: id,
                is_public: isPublic ? 1 : 0
            }, function(result) {
                if (result.code == 0) {
                    $.modal.msgSuccess("设置成功");
                } else {
                    $.modal.alertError("设置失败：" + result.msg);
                    // 恢复checkbox状态
                    $('input[onchange="togglePublic(' + id + ', this.checked)"]').prop('checked', !isPublic);
                }
            }).fail(function() {
                $.modal.alertError("设置失败");
                // 恢复checkbox状态
                $('input[onchange="togglePublic(' + id + ', this.checked)"]').prop('checked', !isPublic);
            });
        }

        // 批量设置公示状态
        function batchSetPublic(isPublic) {
            var rows = $.table.selectFirstColumns();
            if (rows.length == 0) {
                $.modal.alertWarning("请选择要操作的记录");
                return;
            }

            var ids = rows.map(function(row) { return row.id; }).join(',');
            var action = isPublic ? '公示' : '取消公示';

            $.modal.confirm("确定要" + action + "选中的 " + rows.length + " 条记录吗？", function() {
                $.post(prefix + "/moment/batchSetPublic", {
                    ids: ids,
                    is_public: isPublic
                }, function(result) {
                    if (result.code == 0) {
                        $.modal.msgSuccess(result.msg);
                        $.table.refresh();
                    } else {
                        $.modal.alertError("操作失败：" + result.msg);
                    }
                }).fail(function() {
                    $.modal.alertError("操作失败");
                });
            });
        }

        // 显示水印内容详情
        function showContentDetail(id, encodedContent) {
            try {
                var content = decodeURIComponent(encodedContent);
                var contentData = JSON.parse(content);

                var tableHtml = '<div class="table-responsive"><table class="table table-bordered table-striped">';
                tableHtml += '<thead><tr><th style="width: 30%;">字段</th><th style="width: 70%;">值</th></tr></thead><tbody>';

                for (var i = 0; i < contentData.length; i++) {
                    var item = contentData[i];
                    if (Array.isArray(item) && item.length >= 2) {
                        tableHtml += '<tr><td><strong>' + item[0] + '</strong></td><td>' + item[1] + '</td></tr>';
                    }
                }

                tableHtml += '</tbody></table></div>';

                $('#detailContent').html(tableHtml);
                $('#detailModalLabel').text('水印内容详情');
                $('#detailModal').modal('show');
            } catch (e) {
                // 如果解析失败，显示原始内容
                var content = decodeURIComponent(encodedContent);
                $('#detailContent').html('<pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px;">' + content + '</pre>');
                $('#detailModalLabel').text('水印内容详情');
                $('#detailModal').modal('show');
            }
        }
    </script>
</body>
</html>
