// pages/nav/index.js
const app = getApp()
const { getStateManager } = require('../../utils/stateManager')

Page({
  data: {
    menuList: [],
    menuRows: [],
    menuSections: [], // 新增：分类菜单数据
    pageLoading: true,
    communityInfo: {
      communityName: '智慧小区',
      servicePhone: ''
    }
  },

  onLoad() {
    this.getMenuList()
    this.updatePageState()
  },

  // 更新页面状态
  updatePageState() {
    try {
      const stateManager = getStateManager()
      const state = stateManager.getState()

      // 更新社区信息（包含服务电话）
      if (state.communityInfo) {
        this.setData({
          communityInfo: state.communityInfo
        })
      }
    } catch (error) {
      console.warn('[Nav] 更新页面状态失败:', error)
    }
  },

  // 获取菜单列表
  async getMenuList() {
    try {
      wx.showLoading({
        title: '加载中...',
        mask: true
      })

      const res = await app.request({
        url: '/api/wx/index/getAllMenus',  // 修改为getAllMenus接口
        method: 'POST',
        data: {
          source: 'indexNav'  // 使用与首页相同的数据源
        }
      })

      if (res.code === 0) {
        const menuData = res.data || []
        // 处理菜单数据，转换为分类结构
        const menuSections = this.processMenuData(menuData)

        this.setData({
          menuList: menuData,
          menuSections: menuSections,
          pageLoading: false
        })
        // 设置菜单图标颜色
        this.setMenuIconColors()
      } else {
        throw new Error(res.msg || '获取菜单列表失败')
      }
    } catch (error) {
      console.error('获取菜单列表失败:', error)
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      this.setData({
        menuList: [],
        menuSections: [],
        pageLoading: false
      })
    } finally {
      wx.hideLoading()
    }
  },

  // 处理菜单数据，转换为分类结构
  processMenuData(menuData) {
    const sections = []
    const singleItems = []

    menuData.forEach(menu => {
      if (menu.children && menu.children.length > 0) {
        // 有子菜单，作为分类处理
        sections.push({
          type: 'category',
          title: menu.nav_name,
          items: menu.children
        })
      } else {
        // 没有子菜单，作为独立项处理
        singleItems.push(menu)
      }
    })

    // 如果有独立项，添加到sections中，显示"常用服务"标题
    if (singleItems.length > 0) {
      sections.push({
        type: 'category',
        title: '常用服务',
        items: singleItems
      })
    }

    return sections
  },

  // 设置菜单图标颜色
  setMenuIconColors() {
    const colors = [
      'linear-gradient(135deg, #1890ff, #69c0ff)',
      'linear-gradient(135deg, #52c41a, #95de64)',
      'linear-gradient(135deg, #faad14, #ffd666)',
      'linear-gradient(135deg, #ff4d4f, #ff7875)',
      'linear-gradient(135deg, #722ed1, #b37feb)',
      'linear-gradient(135deg, #13c2c2, #5cdbd3)',
      'linear-gradient(135deg, #eb2f96, #f759ab)',
      'linear-gradient(135deg, #fa8c16, #ffb366)'
    ]

    let colorIndex = 0
    const menuSections = this.data.menuSections.map(section => {
      const updatedSection = { ...section }
      updatedSection.items = section.items.map(item => {
        const updatedItem = {
          ...item,
          iconBg: colors[colorIndex % colors.length]
        }
        colorIndex++
        return updatedItem
      })
      return updatedSection
    })

    this.setData({ menuSections })
  },



  // 处理菜单点击
  async handleMenuTap(e) {
    if (!this.checkLoginStatus()) return

    const menu = e.currentTarget.dataset.menu
    if (!menu) return

    // 记录菜单点击日志
    this.recordMenuClick(menu.nav_id, menu.nav_name, menu.nav_type || '', 'nav')

    // 统一获取菜单详情处理所有类型
    try {
      const res = await app.request({
        url: '/api/wx/data/menuContent',
        method: 'POST',
        data: { id: menu.nav_id }
      })

      if (res.code === 0 && res.data) {
        const detail = res.data

        // 根据nav_type处理不同类型
        if (detail.nav_type === 'miniprogram' && detail.miniprogram_config) {
          // 小程序类型，解析配置并跳转
          try {
            const config = JSON.parse(detail.miniprogram_config)
            if (config.appId) {
              this.navigateToMiniprogram(config.appId, config.path, detail.nav_name)
            } else {
              throw new Error('小程序配置中缺少appId')
            }
          } catch (error) {
            console.error('解析小程序配置失败:', error)
            wx.showModal({
              title: '配置错误',
              content: '小程序配置格式错误，请联系管理员',
              showCancel: false,
              confirmText: '知道了'
            })
          }
        } else if (detail.nav_type === 'url' && detail.url) {
          // URL类型，检查是否为微信公众号文章
          if (detail.url.startsWith('https://mp.weixin.qq.com/')) {
            // 微信公众号文章，跳转到微信读书小程序
            this.goToWechatArticle(detail.url)
          } else {
            // 其他URL，直接跳转到webview
            wx.navigateTo({
              url: `/pages/webview/index?url=${encodeURIComponent(detail.url)}&title=${encodeURIComponent(detail.nav_name)}`,
              fail: () => {
                // 如果跳转失败，复制链接到剪贴板
                this.copyUrlToClipboard(detail.url)
              }
            })
          }
        } else if (detail.nav_type === 'page' && detail.tap_name) {
          // 内置页面类型，调用对应的函数
          const tapName = detail.tap_name
          if (tapName && typeof this[tapName] === 'function') {
            this[tapName]()
          } else {
            wx.showToast({
              title: '功能暂未开放',
              icon: 'none'
            })
          }
        } else {
          // 其他类型（text、pdf），跳转到内容页面
          wx.navigateTo({
            url: `/pages/menu/content?id=${menu.nav_id}`
          })
        }
      } else {
        throw new Error(res.msg || '获取菜单内容失败')
      }
    } catch (error) {
      console.error('获取菜单详情失败:', error)
      // 失败时回退到原有逻辑
      wx.navigateTo({
        url: `/pages/menu/content?id=${menu.nav_id}`
      })
    }
  },

  // 跳转到小程序
  navigateToMiniprogram(appId, path, title) {
    // 检查appId格式
    if (!appId || appId.length < 10) {
      wx.showModal({
        title: '小程序ID无效',
        content: `小程序ID格式不正确：${appId}\n\n请检查：\n1. 小程序ID通常以wx开头\n2. 长度应该在18位左右\n3. 确认从正确渠道获取ID`,
        showCancel: false,
        confirmText: '知道了'
      })
      return
    }

    wx.openEmbeddedMiniProgram({
      appId: appId,
      path: path || '',
      extraData: {},
      envVersion: 'release',
      success: () => {
        console.log(`成功跳转到小程序: ${title} (${appId})`)
      },
      fail: (error) => {
        console.error('跳转小程序失败:', error)

        // 如果是用户取消，不显示错误提示
        if (error.errMsg.includes('cancel')) {
          return
        }

        let errorMsg = '跳转失败，可能原因：\n'
        if (error.errMsg.includes('invalid appid')) {
          errorMsg += '• 小程序ID无效或不存在\n• 请确认小程序ID是否正确'
        } else {
          errorMsg += '• 小程序不存在或已下线\n• 网络连接问题\n• 路径参数有误'
        }

        wx.showModal({
          title: '无法跳转小程序',
          content: `目标：${title || appId}\n\n${errorMsg}\n\n调试信息：${error.errMsg}`,
          showCancel: false,
          confirmText: '知道了'
        })
      }
    })
  },

  // 打开微信公众号文章
  goToWechatArticle(url) {
    wx.openOfficialAccountArticle({
      url: url,
      success: () => {
        console.log('成功打开微信公众号文章')
      },
      fail: (err) => {
        console.error('打开微信公众号文章失败:', err)
        // 如果失败，复制链接到剪贴板
        this.copyUrlToClipboard(url)
      }
    })
  },

  // 复制链接到剪贴板
  copyUrlToClipboard(url) {
    wx.setClipboardData({
      data: url,
      success: () => {
        wx.showModal({
          title: '链接已复制',
          content: `由于域名限制，无法直接打开链接。\n链接已复制到剪贴板：\n${url}\n\n请在浏览器中粘贴打开`,
          showCancel: false,
          confirmText: '知道了'
        })
      },
      fail: () => {
        wx.showModal({
          title: '无法打开链接',
          content: `由于域名限制，无法直接打开链接。\n请手动复制以下链接在浏览器中打开：\n${url}`,
          showCancel: false,
          confirmText: '知道了'
        })
      }
    })
  },

  // 导航方法 - 从首页复制
  goToRepair() {
    if (!this.checkLoginAndAuth()) return
    wx.navigateTo({
      url: '/pages/bx/bx'
    })
  },

  goToComplaint() {
    if (!this.checkLoginAndAuth()) return
    wx.navigateTo({
      url: '/pages/complaint/complaint'
    })
  },

  goServiceTel() {
    wx.navigateTo({
      url: '/pages/serviceTel/index'
    })
  },

  goToPayment() {
    if (!this.checkLoginStatus()) return
    wx.navigateTo({
      url: '/pages/charge/bill/bill'
    })
  },

  // 跳转到缴费情况页面（别名方法）
  goToChargeBill() {
    this.goToPayment()
  },

  goToVisitor() {
    this.showComingSoon('邀请住户')
  },

  goToOcInfo() {
    wx.navigateTo({ url: '/pages/ocinfo/ocinfo' })
  },

  goToNoticeList() {
    if (!this.checkLoginStatus()) return
    wx.navigateTo({
      url: '/pages/notice/list'
    })
  },

  goToNewsDetail(e) {
    if (!this.checkLoginStatus()) return

    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/notice/detail?id=${id}`
    })
  },

  goToHouseList() {
    wx.navigateTo({
      url: '/pages/house/index'
    })
  },

  goToPropertyPhone() {
    const phone = this.data.communityInfo?.servicePhone
    if (phone) {
      wx.makePhoneCall({ phoneNumber: phone })
    } else {
      wx.showToast({
        title: '暂无服务电话',
        icon: 'none'
      })
    }
  },

  // 跳转到社区动态页面
  goToMarkicam() {
    console.log('[Nav] 点击社区动态按钮')

    if (!this.checkLoginStatus()) {
      console.log('[Nav] 登录检查失败')
      return
    }

    console.log('[Nav] 准备跳转到markicam页面')
    wx.navigateTo({
      url: '/pages/markicam/index',
      success: () => {
        console.log('[Nav] 跳转成功')
      },
      fail: (error) => {
        console.error('[Nav] 跳转失败:', error)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        })
      }
    })
  },

  // 显示即将推出提示
  showComingSoon(feature) {
    wx.showToast({
      title: `${feature}功能即将推出`,
      icon: 'none'
    })
  },

  /**
   * 记录菜单点击日志
   * @param {string} menuId 菜单ID
   * @param {string} menuName 菜单名称
   * @param {string} menuType 菜单类型
   * @param {string} source 来源页面
   */
  recordMenuClick(menuId, menuName, menuType, source) {
    // 静默记录，不影响用户体验
    try {
      console.log('[Nav] 记录菜单点击:', { menuId, menuName, menuType, source })

      app.request({
        url: '/api/wx/index/recordMenuClick',
        method: 'POST',
        data: {
          menuId: menuId || '',
          menuName: menuName || '',
          menuType: menuType || '',
          source: source || ''
        }
      }).catch(error => {
        // 静默处理错误，不影响用户体验
        console.warn('[Nav] 记录菜单点击日志失败:', error)
      })
    } catch (error) {
      console.warn('[Nav] 记录菜单点击日志异常:', error)
    }
  }
})
