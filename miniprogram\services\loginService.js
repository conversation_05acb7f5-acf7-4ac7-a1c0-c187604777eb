// 登录服务 - 专门处理登录相关的业务逻辑
import { getStateManager } from '../utils/stateManager.js'
import { getLoadingManager } from '../utils/errorHandler.js'
import { TIMEOUT_CONFIG } from '../constants/index.js'
import SecureLogger from '../utils/secureLogger.js'

class LoginService {
  constructor() {
    this.app = getApp()
    this.stateManager = getStateManager()
    this.loadingManager = getLoadingManager()
  }

  /**
   * 检查自动登录
   * @returns {Promise<boolean>} 是否成功自动登录
   */
  async checkAutoLogin() {
    const token = wx.getStorageSync('token')
    const userInfo = wx.getStorageSync('wxUserInfo')

    if (!token || !userInfo?.userId) {
      SecureLogger.log('LoginService', 'Token或用户信息缺失，清除登录状态')
      this.stateManager.clearState(true)
      return false
    }

    // 检查token是否过期（简单的本地检查）
    if (this.isTokenExpiredLocally(token)) {
      SecureLogger.log('LoginService', 'Token本地检查已过期，清除登录状态')
      this.stateManager.clearState(true)
      return false
    }

    // 验证token有效性
    const maxRetries = 2
    let retryCount = 0

    while (retryCount <= maxRetries) {
      try {
        this.loadingManager.show(retryCount === 0 ? '验证登录状态...' : `重试中...`)

        const res = await Promise.race([
          this.app.request({
            url: '/api/wx/auth/check',
            method: 'POST'
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('请求超时')), TIMEOUT_CONFIG.LOGIN)
          )
        ])

        if (res.code === 0) {
          SecureLogger.log('LoginService', 'Token验证成功')
          this.stateManager.updateLoginState({
            isLogin: true,
            token: token,
            userInfo: userInfo
          })
          return true
        }

        SecureLogger.log('LoginService', 'Token验证失败，清除登录状态')
        this.stateManager.clearState(true)
        return false
      } catch (error) {
        SecureLogger.error('LoginService', `自动登录检查失败(第${retryCount + 1}次)`, error)
        
        if (retryCount >= maxRetries) {
          this.stateManager.clearState(true)
          return false
        }
        
        retryCount++
        await new Promise(resolve => setTimeout(resolve, 1000 * retryCount))
      }
    }

    return false
  }

  /**
   * 检查token是否在本地已过期
   * @param {string} token JWT token
   * @returns {boolean} 是否过期
   */
  isTokenExpiredLocally(token) {
    try {
      // 简单的本地过期检查，避免解析JWT
      const tokenTime = wx.getStorageSync('tokenTime')
      if (tokenTime) {
        const now = Date.now()
        const tokenAge = now - tokenTime
        // 如果token超过7天，认为可能过期
        if (tokenAge > 7 * 24 * 60 * 60 * 1000) {
          SecureLogger.log('LoginService', 'Token存储时间超过7天，可能已过期')
          return true
        }
      }

      return false
    } catch (error) {
      SecureLogger.error('LoginService', 'Token过期检查失败', error)
      return false
    }
  }

  /**
   * 执行微信登录（统一使用smartLogin接口）
   * @param {Object} loginParams 登录参数
   * @returns {Promise<Object>} 登录结果
   */
  async performWxLogin(loginParams) {
    try {
      const { code, phoneCode, encryptedData, iv, checkResult } = loginParams

      // 构建登录数据
      const loginData = {}

      // 添加code参数（仅在没有checkResult或首次登录时）
      if (code && !checkResult) {
        loginData.code = code
      }

      // 添加手机号授权码
      if (phoneCode) {
        loginData.phoneCode = phoneCode
      }

      // 添加加密用户信息
      if (encryptedData && iv && checkResult?.sessionKey) {
        loginData.sessionKey = checkResult.sessionKey
        loginData.encryptedData = encryptedData
        loginData.iv = iv
        SecureLogger.log('LoginService', '包含加密用户信息，需要sessionKey解密')
      }

      // 添加检查结果（避免重复调用微信接口）
      if (checkResult) {
        loginData.openid = checkResult.openid
        loginData.unionid = checkResult.unionid
        loginData.sessionKey = checkResult.sessionKey
        loginData.userExists = checkResult.userExists
        SecureLogger.log('LoginService', '使用checkResult，跳过code传递避免重复使用')
      }

      SecureLogger.log('LoginService', '执行智能登录请求', {
        hasCode: !!loginData.code,
        hasPhoneCode: !!phoneCode,
        hasEncryptedData: !!(encryptedData && iv),
        hasCheckResult: !!checkResult,
        dataSize: JSON.stringify(loginData).length + ' bytes'
      })

      // 调用智能登录接口
      this.loadingManager.show('正在验证登录...')
      const res = await this.app.request({
        url: '/api/wx/auth/smartLogin',
        method: 'POST',
        data: loginData
      })

      if (res.code === 0) {
        SecureLogger.log('LoginService', '智能登录成功')
        return res.data
      } else {
        throw new Error(res.msg || '登录失败')
      }
    } catch (error) {
      SecureLogger.error('LoginService', '微信登录失败', error)
      throw error
    }
  }

  /**
   * 处理登录成功结果
   * @param {Object} loginResult 登录结果
   */
  handleLoginSuccess(loginResult) {
    if (!loginResult) {
      throw new Error('服务器返回数据为空')
    }

    // 检查是否需要身份选择
    if (loginResult.needIdentitySelection) {
      SecureLogger.log('LoginService', '需要身份选择')
      SecureLogger.log('LoginService', '原始登录结果:', loginResult)

      const loginParams = {
        openid: loginResult.openid || null,
        unionid: loginResult.unionid || null,
        sessionKey: loginResult.sessionKey || null,
        phoneNumber: loginResult.phoneNumber || null,
        encryptedData: loginResult.encryptedData || null,
        iv: loginResult.iv || null
      }

      SecureLogger.log('LoginService', '构造的登录参数:', loginParams)

      return {
        needIdentitySelection: true,
        identityResult: loginResult.identityResult,
        loginParams: loginParams
      }
    }

    // 构建登录数据
    const loginData = {
      userInfo: loginResult.userInfo,
      hasBindPhone: loginResult.hasBindPhone,
      isHouseAuth: loginResult.isHouseAuth,
      authStatus: loginResult.authStatus,
      ownerInfo: loginResult.ownerInfo,
      communityInfo: loginResult.communityInfo,
      userType: loginResult.userType || '1', // 默认业主
      isPropertyUser: loginResult.isPropertyUser || false
    }

    // 只有房屋认证成功或物业用户才设置token
    if ((loginResult.isHouseAuth || loginResult.isPropertyUser) && loginResult.token) {
      loginData.token = loginResult.token
      SecureLogger.log('LoginService', '认证成功，设置token')
    } else {
      SecureLogger.log('LoginService', '认证未完成，不设置token')
    }

    this.stateManager.setLoginSuccess(loginData)

    // 登录成功后初始化TabBar配置
    this.initTabBarConfig(loginData.userType)

    return loginData
  }

  /**
   * 清除登录状态
   */
  clearLoginState() {
    this.stateManager.clearState(true)
  }

  /**
   * 初始化TabBar配置
   * @param {string} userType 用户类型
   */
  initTabBarConfig(userType) {
    try {
      // 获取当前页面的TabBar实例
      const pages = getCurrentPages()
      if (pages.length > 0) {
        const currentPage = pages[pages.length - 1]
        if (typeof currentPage.getTabBar === 'function' && currentPage.getTabBar()) {
          currentPage.getTabBar().updateTabBarConfig(userType)
          SecureLogger.log('LoginService', `TabBar配置已初始化: userType=${userType}`)
        }
      }
    } catch (error) {
      SecureLogger.error('LoginService', '初始化TabBar配置失败', error)
    }
  }

  /**
   * 身份选择登录
   * @param {string} userType 用户类型 "1"=业主, "2"=物业
   * @param {Object} loginParams 登录参数
   * @param {Object} identityResult 身份识别结果
   * @returns {Promise<Object>} 登录结果
   */
  async selectIdentityLogin(userType, loginParams, identityResult) {
    try {
      SecureLogger.log('LoginService', `身份选择登录: userType=${userType}`)
      SecureLogger.log('LoginService', '登录参数:', loginParams)

      // 过滤掉undefined值，避免发送无效参数
      const filteredParams = {}
      Object.keys(loginParams).forEach(key => {
        if (loginParams[key] !== undefined && loginParams[key] !== null) {
          filteredParams[key] = loginParams[key]
        }
      })

      const requestData = {
        userType,
        ...filteredParams
      }

      SecureLogger.log('LoginService', '过滤后的请求数据:', requestData)

      // 传递用户信息，避免后端重复查询
      if (identityResult) {
        if (userType === '1' && identityResult.ownerUser) {
          requestData.ownerUser = identityResult.ownerUser
        }
        if (userType === '2' && identityResult.propertyUser) {
          requestData.propertyUser = identityResult.propertyUser
        }
      }

      const res = await this.app.request({
        url: '/api/wx/auth/selectIdentity',
        method: 'POST',
        data: requestData
      })

      if (res.code === 0) {
        SecureLogger.log('LoginService', '身份选择登录成功')
        return this.handleLoginSuccess(res.data)
      } else {
        throw new Error(res.msg || '身份选择登录失败')
      }
    } catch (error) {
      SecureLogger.error('LoginService', '身份选择登录失败', error)
      throw error
    }
  }

  /**
   * 绑定手机号
   * @param {string} phoneCode 手机号授权码
   * @param {string} userId 用户ID
   * @returns {Promise<Object>} 绑定结果
   */
  async bindPhoneNumber(phoneCode, userId) {
    try {
      // 获取微信登录凭证
      this.loadingManager.show('获取登录凭证...')
      const loginResult = await wx.login()

      if (!loginResult.code) {
        throw new Error('获取微信登录凭证失败')
      }

      // 调用绑定手机号接口
      this.loadingManager.show('正在绑定手机号...')
      const res = await this.app.request({
        url: '/api/wx/auth/bindPhone',
        method: 'POST',
        data: {
          code: loginResult.code,
          phoneCode: phoneCode,
          userId: userId
        }
      })

      if (res.code === 0) {
        SecureLogger.log('LoginService', '手机号绑定成功')
        return res.data
      } else {
        throw new Error(res.msg || '手机号绑定失败')
      }
    } catch (error) {
      SecureLogger.error('LoginService', '手机号绑定失败', error)
      throw error
    }
  }

  /**
   * 跳转到首页
   */
  redirectToHome() {
    const state = this.stateManager.getState()
    const userType = state.userType || '1'

    if (userType === '2') {
      // 物业用户跳转到工作台
      wx.switchTab({
        url: '/pages/property/dashboard/index'
      })
    } else {
      // 业主用户跳转到首页
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  }
}

// 创建单例
let loginService = null

export function getLoginService() {
  if (!loginService) {
    loginService = new LoginService()
  }
  return loginService
}

export default getLoginService
