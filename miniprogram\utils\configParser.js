// 配置解析器 - 专门处理extJson和infoJson等配置解析
class ConfigParser {
  /**
   * 解析extJson配置（小配置）
   * @param {Object} communityInfo 社区信息
   * @returns {Object|null} 解析后的配置对象
   */
  static parseExtJson(communityInfo) {
    if (!communityInfo || !communityInfo.extJson) {
      return null
    }

    try {
      let extConfig = null

      if (typeof communityInfo.extJson === 'string') {
        const jsonStr = communityInfo.extJson.trim()
        if (!jsonStr) {
          console.warn('[ConfigParser] extJson字符串为空')
          return null
        }

        if (!jsonStr.startsWith('{') && !jsonStr.startsWith('[')) {
          console.warn('[ConfigParser] extJson不是有效的JSON格式')
          return null
        }

        extConfig = JSON.parse(jsonStr)
      } else if (typeof communityInfo.extJson === 'object' && communityInfo.extJson !== null) {
        extConfig = communityInfo.extJson
      } else {
        console.warn('[ConfigParser] extJson类型不支持:', typeof communityInfo.extJson)
        return null
      }

      if (!extConfig || typeof extConfig !== 'object') {
        console.warn('[ConfigParser] 解析后的extConfig不是对象')
        return null
      }

      return this.normalizeExtConfig(extConfig)
    } catch (error) {
      console.error('[ConfigParser] 解析extJson失败:', error)
      return null
    }
  }

  /**
   * 解析infoJson配置（小区扩展信息）
   * @param {Object} communityInfo 社区信息
   * @returns {Object|null} 解析后的小区扩展信息对象
   */
  static parseInfoJson(communityInfo) {
    if (!communityInfo || !communityInfo.infoJson) {
      return null
    }

    try {
      let infoConfig = null

      if (typeof communityInfo.infoJson === 'string') {
        const jsonStr = communityInfo.infoJson.trim()
        if (!jsonStr) {
          console.warn('[ConfigParser] infoJson字符串为空')
          return null
        }

        if (!jsonStr.startsWith('{') && !jsonStr.startsWith('[')) {
          console.warn('[ConfigParser] infoJson不是有效的JSON格式')
          return null
        }

        infoConfig = JSON.parse(jsonStr)
      } else if (typeof communityInfo.infoJson === 'object' && communityInfo.infoJson !== null) {
        infoConfig = communityInfo.infoJson
      } else {
        console.warn('[ConfigParser] infoJson类型不支持:', typeof communityInfo.infoJson)
        return null
      }

      if (!infoConfig || typeof infoConfig !== 'object') {
        console.warn('[ConfigParser] 解析后的infoConfig不是对象')
        return null
      }

      return this.normalizeInfoConfig(infoConfig)
    } catch (error) {
      console.error('[ConfigParser] 解析infoJson失败:', error)
      return null
    }
  }

  /**
   * 标准化extConfig配置
   * @param {Object} extConfig 原始配置
   * @returns {Object} 标准化后的配置
   */
  static normalizeExtConfig(extConfig) {
    return {
      // 小程序首页配置
      miniprogram_title: this.safeGetString(extConfig.miniprogram_title),
      miniprogram_subtitle: this.safeGetString(extConfig.miniprogram_subtitle),
      enable_miniprogram_title: this.safeGetString(extConfig.enable_miniprogram_title, '0'),

      // 功能控制配置
      enable_miniprogram_mark: this.safeGetString(extConfig.enable_miniprogram_mark, '0'),
      enable_screenshot: this.safeGetString(extConfig.enable_screenshot, '1'),
      enable_comment: this.safeGetString(extConfig.enable_comment, '1'),

      // 布局配置
      menu_rows: this.safeGetString(extConfig.menu_rows, '4'),
      show_big_card: this.safeGetString(extConfig.show_big_card, '1'),

      //导航菜单
      enable_financial: this.safeGetString(extConfig.enable_financial, '0'),
      enable_markicam: this.safeGetString(extConfig.enable_markicam, '0'),

      // 分享配置
      enable_share: this.safeGetString(extConfig.enable_share, '1'),
      share_title: this.safeGetString(extConfig.share_title, '智慧社区服务'),
      share_desc: this.safeGetString(extConfig.share_desc, '一键触达生活所需，物业服务就在掌心'),
      share_image: this.safeGetString(extConfig.share_image, '/static/images/share-cover.png'),
      enable_share_statistics: this.safeGetString(extConfig.enable_share_statistics, '1'),
      show_sharer_info: this.safeGetString(extConfig.show_sharer_info, '1')
    }
  }

  /**
   * 标准化infoConfig配置（小区扩展信息）
   * @param {Object} infoConfig 原始小区扩展信息
   * @returns {Object} 标准化后的小区扩展信息
   */
  static normalizeInfoConfig(infoConfig) {
    return {
      // 费用信息
      property_fee: this.safeGetString(infoConfig.property_fee),
      parking_fee: this.safeGetString(infoConfig.parking_fee),

      // 小区参数
      green_rate: this.safeGetString(infoConfig.green_rate),
      plot_ratio: this.safeGetString(infoConfig.plot_ratio),

      // 开发信息
      developer: this.safeGetString(infoConfig.developer),
      completion_date: this.safeGetString(infoConfig.completion_date),

      // 配套设施
      heating_type: this.safeGetString(infoConfig.heating_type),
      water_supply: this.safeGetString(infoConfig.water_supply),
      power_supply: this.safeGetString(infoConfig.power_supply),
      gas_supply: this.safeGetString(infoConfig.gas_supply),

      // 小区简介
      intro: this.safeGetString(infoConfig.intro)
    }
  }

  /**
   * 安全获取字符串值
   * @param {*} value 原始值
   * @param {string} defaultValue 默认值
   * @returns {string} 字符串值
   */
  static safeGetString(value, defaultValue = '') {
    if (value === null || value === undefined || value === '') {
      return defaultValue
    }
    if (typeof value === 'string') {
      return value
    }
    if (typeof value === 'number' || typeof value === 'boolean') {
      return String(value)
    }
    return defaultValue
  }
}

export default ConfigParser
